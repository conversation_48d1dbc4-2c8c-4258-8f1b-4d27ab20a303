package com.xhcai.modules.rag.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xhcai.modules.rag.dto.KnowledgeDocumentDTO;
import com.xhcai.modules.rag.entity.Dataset;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig;
import com.xhcai.modules.rag.mapper.DatasetMapper;
import com.xhcai.modules.rag.mapper.DocumentMapper;
import com.xhcai.modules.rag.service.IKnowledgeDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class KnowledgeDocumentServiceImpl implements IKnowledgeDocumentService {
    @Autowired
    private DocumentMapper documentMapper;
    @Autowired
    private DatasetMapper datasetMapper;
    @Override
    public boolean embedding(KnowledgeDocumentDTO knowledgeDocumentDTO) {
        Document document = documentMapper.selectById(knowledgeDocumentDTO.getDocumentId());
        if (document == null) {
            log.error("文档不存在: documentId={}", knowledgeDocumentDTO.getDocumentId());
            return false;
        }
        String datasetId = document.getDatasetId();
        Dataset dataset = datasetMapper.selectById(datasetId);
        if (dataset == null) {
            log.error("知识库不存在: datasetId={}", datasetId);
            return false;
        }

        Map<String, Object> vectorizationConfig = dataset.getVectorizationConfig();
        KnowledgeVectorizationConfig knowledgeVectorizationConfig = BeanUtil.toBean(vectorizationConfig, KnowledgeVectorizationConfig.class);

        return true;
    }

    @Override
    public void segmentation(KnowledgeDocumentDTO knowledgeDocumentDTO) {

    }
}
