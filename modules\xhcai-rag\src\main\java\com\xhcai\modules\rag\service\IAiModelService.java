package com.xhcai.modules.rag.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.rag.dto.AiModelCreateDTO;
import com.xhcai.modules.rag.dto.AiModelQueryDTO;
import com.xhcai.modules.rag.dto.AiModelUpdateDTO;
import com.xhcai.modules.rag.entity.AiModel;
import com.xhcai.modules.rag.vo.AiModelVO;
import org.springframework.ai.embedding.EmbeddingModel;

import java.util.List;
import java.util.Map;

/**
 * AI模型配置服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IAiModelService extends IService<AiModel> {

    /**
     * 分页查询AI模型列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<AiModelVO> selectAiModelPage(AiModelQueryDTO queryDTO);

    /**
     * 查询AI模型列表
     *
     * @param queryDTO 查询条件
     * @return AI模型列表
     */
    List<AiModelVO> selectAiModelList(AiModelQueryDTO queryDTO);

    /**
     * 根据ID查询AI模型详情
     *
     * @param id 模型ID
     * @return AI模型详情
     */
    AiModelVO selectAiModelById(String id);

    /**
     * 创建AI模型
     *
     * @param createDTO 创建参数
     * @return 是否成功
     */
    boolean createAiModel(AiModelCreateDTO createDTO);

    /**
     * 更新AI模型
     *
     * @param updateDTO 更新参数
     * @return 是否成功
     */
    boolean updateAiModel(AiModelUpdateDTO updateDTO);

    /**
     * 删除AI模型
     *
     * @param id 模型ID
     * @return 是否成功
     */
    boolean deleteAiModel(String id);

    /**
     * 批量删除AI模型
     *
     * @param ids 模型ID列表
     * @return 是否成功
     */
    boolean batchDeleteAiModels(List<String> ids);

    /**
     * 检查模型标识是否存在
     *
     * @param modelId 模型标识
     * @param excludeId 排除的模型ID
     * @return 是否存在
     */
    boolean existsModelId(String modelId, String excludeId);

    /**
     * 检查模型名称是否存在
     *
     * @param name 模型名称
     * @param excludeId 排除的模型ID
     * @return 是否存在
     */
    boolean existsModelName(String name, String excludeId);

    /**
     * 根据提供商查询模型列表
     *
     * @param provider 提供商
     * @return 模型列表
     */
    List<AiModelVO> selectByProvider(String provider);

    /**
     * 根据类型查询模型列表
     *
     * @param type 模型类型
     * @return 模型列表
     */
    List<AiModelVO> selectByType(String type);

    /**
     * 启用模型
     *
     * @param id 模型ID
     * @return 是否成功
     */
    boolean enableAiModel(String id);

    /**
     * 停用模型
     *
     * @param id 模型ID
     * @return 是否成功
     */
    boolean disableAiModel(String id);

    /**
     * 统计各提供商的模型数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> countByProvider();

    /**
     * 统计各类型的模型数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> countByType();


    /**
     * 测试模型连接
     *
     * @param id 模型ID
     * @return 测试结果
     */
    Map<String, Object> testModelConnection(String id);

    /**
     * 获取嵌入模型
     *
     * @param id 模型ID
     * @return 嵌入模型
     */
    EmbeddingModel getEmbeddingOpenApi(String id);
}
