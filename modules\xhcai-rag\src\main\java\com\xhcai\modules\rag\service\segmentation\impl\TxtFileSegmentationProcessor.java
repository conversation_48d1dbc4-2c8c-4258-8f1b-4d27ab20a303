package com.xhcai.modules.rag.service.segmentation.impl;

import cn.hutool.core.util.StrUtil;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import com.xhcai.modules.rag.service.segmentation.AbstractFileSegmentationProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * TXT文件分段处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class TxtFileSegmentationProcessor extends AbstractFileSegmentationProcessor {

    @Override
    public List<String> getSupportedFileTypes() {
        return Arrays.asList("txt","log","js","ts","jsx","tsx","vue","html","css","scss","sass","less","json","xml","yaml","yml","py","java","cpp","c","cs","php","rb","go","rs","swift","kt","sql","ini","conf","config","env");
    }

    @Override
    public String getProcessorName() {
        return "TXT文件分段处理器";
    }

    @Override
    public int getPriority() {
        return 10;
    }

    @Override
    public List<SegmentResult> processSegmentation(Document document, InputStream inputStream) throws Exception {
        log.info("开始处理TXT文件分段: documentId={}, fileName={}", document.getId(), document.getName());
        try {
            SegmentConfig segmentConfig = document.getSegmentConfig();
            CleaningConfig cleaningConfig = document.getCleaningConfig();
            String type = segmentConfig.getType();

            // 读取文件内容
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    line = cleanText(line, cleaningConfig);
                    content.append(line).append(StrUtil.LF);
                }
            }

            String text = content.toString();
            List<SegmentResult> segments = null;
            switch (type) {
                case "directory":
                    // txt结构文档，按目录结构分段时，默认是5个自然段为一个分段
                    SegmentConfig.NaturalConfig naturalConfig = new SegmentConfig.NaturalConfig();
                    naturalConfig.setSegments(5);
                    segments = segmentByParagraphs(text, naturalConfig);
                    break;
                case "constantLength":
                    segments = segmentByFixedSize(text, segmentConfig.getConstantLength());
                    break;
                case "natural":
                    segments = segmentByParagraphs(text, segmentConfig.getNatural());
                    break;
                case "delimiter":
                    segments = segmentByDelimiter(text, segmentConfig.getDelimiter());
                    break;
                case "none":
                    segments = List.of(SegmentResult.create(text, 1, extractKeywords(text)));
                    break;
            }

            log.info("Text文件分段完成: documentId={}, 分段数量={}", document.getId(), segments.size());
            return segments;
        } catch (Exception e) {
            log.error("Text文档分段处理失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            throw new Exception("Text文档分段处理失败: " + e.getMessage(), e);
        }
    }
}
