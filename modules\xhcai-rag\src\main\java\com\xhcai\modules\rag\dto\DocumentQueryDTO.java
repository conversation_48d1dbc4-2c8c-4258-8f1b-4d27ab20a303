package com.xhcai.modules.rag.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文档查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "文档查询DTO")
public class DocumentQueryDTO extends PageTimeRangeQueryDTO {

    @Schema(description = "知识库ID", example = "dataset_123")
    private String datasetId;

    @Schema(description = "文档名称", example = "技术文档.pdf")
    private String name;

    @Schema(description = "数据源类型", example = "document")
    private String dataSourceType;

    @Schema(description = "索引状态", example = "completed")
    private String documentStatus;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    @Schema(description = "是否暂停", example = "false")
    private Boolean isPaused;

    @Schema(description = "是否归档", example = "false")
    private Boolean archived;

    @Schema(description = "批次号", example = "batch_20231201_001")
    private String batch;

    @Schema(description = "文档类型", example = "pdf")
    private String docType;

    @Schema(description = "分类ID", example = "category_123")
    private String categoryId;

    @Override
    public String toString() {
        return "DocumentQueryDTO{"
                + "datasetId='" + datasetId + '\''
                + ", name='" + name + '\''
                + ", dataSourceType='" + dataSourceType + '\''
                + ", documentStatus='" + documentStatus + '\''
                + ", enabled=" + enabled
                + ", isPaused=" + isPaused
                + ", archived=" + archived
                + ", batch='" + batch + '\''
                + ", docType='" + docType + '\''
                + ", categoryId='" + categoryId + '\''
                + ", current=" + getCurrent()
                + ", size=" + getSize()
                + ", beginTime=" + getBeginTime()
                + ", endTime=" + getEndTime()
                + '}';
    }
}
