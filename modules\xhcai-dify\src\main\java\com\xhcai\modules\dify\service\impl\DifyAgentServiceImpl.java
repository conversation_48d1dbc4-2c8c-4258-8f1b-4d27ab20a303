package com.xhcai.modules.dify.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.config.DifyWebClientConfig;
import com.xhcai.modules.dify.dto.DifyResponse;
import com.xhcai.modules.dify.dto.agent.DifyAgentDTO;
import com.xhcai.modules.dify.dto.agent.DifyChatRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyChatResponseDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCloneRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateResponseDTO;
import com.xhcai.modules.dify.service.IDifyAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 智能体服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class DifyAgentServiceImpl implements IDifyAgentService {

    private static final Logger log = LoggerFactory.getLogger(DifyAgentServiceImpl.class);


    @Autowired
    private DifyWebClientConfig difyWebClientConfig;




    @Override
    public Mono<Result<com.xhcai.modules.dify.dto.app.DifyAppsListResponseDTO>> getAgentsList(String platformId, Integer page, Integer limit, String name, Boolean isCreatedByMe) {
        log.info("获取平台{}的智能体列表: page={}, limit={}, name={}, isCreatedByMe={}", platformId, page, limit, name, isCreatedByMe);

        return difyWebClientConfig.getAppsListWithAuth(platformId, page, limit, name, isCreatedByMe)
                .map(Result::success)
                .doOnNext(result -> log.debug("获取平台{}的智能体列表成功: {} 个智能体", platformId,
                    result.getData() != null && result.getData().getData() != null ? result.getData().getData().size() : 0))
                .doOnError(error -> log.error("获取平台{}的智能体列表失败", platformId, error))
                .onErrorResume(throwable -> {
                    log.error("获取平台{}的智能体列表异常", platformId, throwable);
                    String errorMessage = "获取平台" + platformId + "的智能体列表失败";
                    if (throwable instanceof WebClientResponseException) {
                        WebClientResponseException ex = (WebClientResponseException) throwable;
                        HttpStatus status = (HttpStatus) ex.getStatusCode();
                        if (status == HttpStatus.UNAUTHORIZED) {
                            errorMessage = "平台" + platformId + "认证失败，请检查登录状态";
                        } else if (status == HttpStatus.FORBIDDEN) {
                            errorMessage = "没有权限访问平台" + platformId + "的智能体列表";
                        } else if (status.is5xxServerError()) {
                            errorMessage = "平台" + platformId + "服务器内部错误，请稍后重试";
                        }
                    }
                    return Mono.just(Result.fail(errorMessage));
                });
    }



    @Override
    public Mono<Result<DifyAgentCreateResponseDTO>> createThirdPartyAgent(DifyAgentCreateRequestDTO createRequest) {
        log.info("创建指定平台{}的第三方智能体: {}", createRequest);

        // 构建请求体，使用下划线命名方式
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", createRequest.getName());
        requestBody.put("icon_type", "emoji");
        requestBody.put("icon", "🤖");
        requestBody.put("icon_background", "#FFEAD5");
        requestBody.put("mode", createRequest.getMode());
        requestBody.put("description", createRequest.getDescription());

        return difyWebClientConfig.makeAuthenticatedPostRequestForPlatform(createRequest.getPlatformId(), "/console/api/apps", requestBody, String.class)
                .map(response -> {
                    log.info("平台{}创建智能体响应: {}", createRequest.getPlatformId(), response);
                    try {
                        DifyAgentCreateResponseDTO responseDTO = JSON.parseObject(response, DifyAgentCreateResponseDTO.class);
                        return Result.success(responseDTO);
                    } catch (Exception e) {
                        log.error("解析平台{}响应失败: {}",createRequest.getPlatformId(), response, e);
                        throw new BusinessException("解析响应失败: " + e.getMessage());
                    }
                })
                .doOnError(error -> log.error("创建平台{}的第三方智能体失败", createRequest.getPlatformId(), error))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyAgentCreateResponseDTO>> cloneThirdPartyAgent(DifyAgentCloneRequestDTO cloneRequest) {
        log.info("克隆指定平台{}的第三方智能体: {}", cloneRequest.getPlatformId(), cloneRequest);

        // 构建请求体，使用下划线命名方式
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", cloneRequest.getName());
        requestBody.put("icon_type", "emoji");
        requestBody.put("icon", "🤖");
        requestBody.put("icon_background", "#FFEAD5");
        requestBody.put("mode", cloneRequest.getMode());

        // 构建克隆API路径
        String apiPath = "/console/api/apps/" + cloneRequest.getSourceAppId() + "/copy";

        return difyWebClientConfig.makeAuthenticatedPostRequestForPlatform(cloneRequest.getPlatformId(), apiPath, requestBody, String.class)
                .map(response -> {
                    log.info("平台{}克隆智能体响应: {}", cloneRequest.getPlatformId(), response);
                    try {
                        DifyAgentCreateResponseDTO responseDTO = JSON.parseObject(response, DifyAgentCreateResponseDTO.class);
                        return Result.success(responseDTO);
                    } catch (Exception e) {
                        log.error("解析平台{}响应失败: {}", cloneRequest.getPlatformId(), response, e);
                        throw new BusinessException("解析响应失败: " + e.getMessage());
                    }
                })
                .doOnError(error -> log.error("克隆平台{}的第三方智能体失败", cloneRequest.getPlatformId(), error))
                .onErrorResume(this::handleError);
    }

    /**
     * 统一错误处理
     */
    private <T> Mono<Result<T>> handleError(Throwable throwable) {
        log.error("Dify API调用失败", throwable);
        
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            HttpStatus status = (HttpStatus) ex.getStatusCode();
            String message = "API调用失败: " + status.getReasonPhrase();
            
            if (status == HttpStatus.UNAUTHORIZED) {
                message = "API密钥无效或已过期";
            } else if (status == HttpStatus.FORBIDDEN) {
                message = "没有权限访问该资源";
            } else if (status == HttpStatus.NOT_FOUND) {
                message = "请求的资源不存在";
            } else if (status == HttpStatus.TOO_MANY_REQUESTS) {
                message = "请求过于频繁，请稍后重试";
            } else if (status.is5xxServerError()) {
                message = "服务器内部错误，请稍后重试";
            }
            
            return Mono.just(Result.fail(status.value(), message));
        } else if (throwable instanceof BusinessException) {
            BusinessException ex = (BusinessException) throwable;
            return Mono.just(Result.fail(500, ex.getMessage()));
        } else {
            return Mono.just(Result.fail(500, "系统异常: " + throwable.getMessage()));
        }
    }
}
