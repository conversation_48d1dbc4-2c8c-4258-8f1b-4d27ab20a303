package com.xhcai.common.core.service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.concurrent.TimeUnit;

/**
 * 节点注册服务
 */
@Service
@Slf4j
public class StableNodeRegistrationService {

    private final StringRedisTemplate redisTemplate;

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${spring.application.name:xhcai-plus}")
    private String applicationName;

    private static final String REDIS_NODE_KEY_PREFIX = "xhcai:node:";
    private static final long REDIS_NODE_EXPIRE_SECONDS = 60;

    @Getter
    private String nodeId;

    @Getter
    private String redisKey;

    public StableNodeRegistrationService(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 生成基于机器硬件的稳定ID
     */
    private String generateStableNodeId() {
        try {
            // 1. 获取机器唯一标识（MAC地址 + 机器名）
            String machineFingerprint = getMachineFingerprint();

            // 2. 结合应用名称和端口
            String uniqueSeed = machineFingerprint + ":" + applicationName + ":" + serverPort;

            // 3. 生成SHA-256哈希作为稳定ID
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(uniqueSeed.getBytes(StandardCharsets.UTF_8));

            // 取前8字节转换为16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (int i = 0; i < 8; i++) {
                String hex = Integer.toHexString(0xff & hash[i]);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (Exception e) {
            log.warn("生成稳定节点ID失败，使用备用方案", e);
            // 备用方案：使用UUID（不保证同一机器每次相同）
            return "fallback-" + java.util.UUID.randomUUID().toString().substring(0, 8);
        }
    }

    /**
     * 获取机器指纹（MAC地址 + 机器名）
     */
    private String getMachineFingerprint() {
        try {
            StringBuilder fingerprint = new StringBuilder();

            // 获取机器名
            String hostname = java.net.InetAddress.getLocalHost().getHostName();
            fingerprint.append(hostname).append(":");

            // 获取第一个非回环网卡的MAC地址
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                if (!networkInterface.isLoopback() && networkInterface.isUp() &&
                        !networkInterface.isVirtual() && networkInterface.getHardwareAddress() != null) {
                    byte[] mac = networkInterface.getHardwareAddress();
                    StringBuilder macBuilder = new StringBuilder();
                    for (int i = 0; i < mac.length; i++) {
                        macBuilder.append(String.format("%02X", mac[i]));
                        if (i < mac.length - 1) {
                            macBuilder.append(":");
                        }
                    }
                    fingerprint.append(macBuilder);
                    break; // 取第一个有效的MAC地址
                }
            }

            return fingerprint.toString();

        } catch (Exception e) {
            log.warn("获取机器指纹失败", e);
            return "unknown-machine";
        }
    }

    @PostConstruct
    public void registerNode() {
        try {
            // 生成稳定节点ID
            nodeId = generateStableNodeId();
            redisKey = REDIS_NODE_KEY_PREFIX + nodeId;

            String nodeInfo = buildNodeInfo();

            // 尝试注册节点
            Boolean success = redisTemplate.opsForValue().setIfAbsent(redisKey, nodeInfo,
                    REDIS_NODE_EXPIRE_SECONDS, TimeUnit.SECONDS);

            if (Boolean.TRUE.equals(success)) {
                log.info("节点成功注册到Redis. ID: {}, Info: {}", nodeId, nodeInfo);
            } else {
                log.warn("节点ID冲突: {}", nodeId);
                handleIdConflict(nodeInfo);
            }

            renewNodeRegistration();

        } catch (Exception e) {
            log.error("注册节点到Redis时发生错误", e);
        }
    }

    /**
     * 处理ID冲突
     */
    private void handleIdConflict(String baseNodeInfo) {
        try {
            // 检查现有节点是否活跃
            String existingInfo = redisTemplate.opsForValue().get(redisKey);
            if (existingInfo != null && existingInfo.contains("Port: " + serverPort)) {
                // 如果是同一应用的同一端口，可能是重启，直接续期
                log.info("检测到自身重启，重新接管节点ID: {}", nodeId);
                redisTemplate.opsForValue().set(redisKey, buildNodeInfo(),
                        REDIS_NODE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            } else {
                // 真正的冲突，生成新ID
                String newId = nodeId + "-" + System.currentTimeMillis();
                log.info("节点ID冲突，生成新ID: {} -> {}", nodeId, newId);
                nodeId = newId;
                redisKey = REDIS_NODE_KEY_PREFIX + nodeId;
                redisTemplate.opsForValue().set(redisKey, buildNodeInfo() + " (Regenerated due to conflict)",
                        REDIS_NODE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("处理ID冲突时发生错误", e);
        }
    }

    @Scheduled(fixedRate = 45000)
    public void renewNodeRegistration() {
        try {
            if (redisKey != null) {
                String nodeInfo = buildNodeInfo();

                redisTemplate.opsForValue().set(redisKey, nodeInfo,
                        REDIS_NODE_EXPIRE_SECONDS, TimeUnit.SECONDS);
                log.debug("节点注册已续期: {}", nodeId);
            }
        } catch (Exception e) {
            log.error("续期节点注册时发生错误", e);
        }
    }

    @PreDestroy
    public void unregisterNode() {
        try {
            if (redisKey != null) {
                Boolean deleted = redisTemplate.delete(redisKey);
                if (deleted) {
                    log.info("节点已从Redis注销: {}", nodeId);
                }
            }
        } catch (Exception e) {
            log.error("从Redis注销节点时发生错误", e);
        }
    }

    /**
     * 构建详细的节点信息
     */
    private String buildNodeInfo() {
        try {
            StringBuilder nodeInfo = new StringBuilder();

            // 基本信息
            nodeInfo.append("App: ").append(applicationName)
                   .append(", Port: ").append(serverPort)
                   .append(", NodeId: ").append(nodeId);

            // 本地IP信息
            String localIp = getLocalIpAddress();
            if (localIp != null) {
                nodeInfo.append(", LocalIP: ").append(localIp);
            }

            // 主机名
            try {
                String hostname = InetAddress.getLocalHost().getHostName();
                nodeInfo.append(", Hostname: ").append(hostname);
            } catch (Exception e) {
                log.debug("获取主机名失败", e);
            }

            // 系统信息
            nodeInfo.append(", OS: ").append(System.getProperty("os.name"))
                   .append(" ").append(System.getProperty("os.version"))
                   .append(" (").append(System.getProperty("os.arch")).append(")");

            // Java版本
            nodeInfo.append(", Java: ").append(System.getProperty("java.version"));

            // 可用处理器数量
            nodeInfo.append(", CPUs: ").append(Runtime.getRuntime().availableProcessors());

            // 内存信息（MB）
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory() / 1024 / 1024;
            long totalMemory = runtime.totalMemory() / 1024 / 1024;
            long freeMemory = runtime.freeMemory() / 1024 / 1024;
            nodeInfo.append(", Memory: ").append(totalMemory).append("/").append(maxMemory)
                   .append("MB (Free: ").append(freeMemory).append("MB)");

            // 时间信息
            nodeInfo.append(", RegisteredAt: ").append(LocalDateTime.now());

            return nodeInfo.toString();

        } catch (Exception e) {
            log.warn("构建节点信息失败，使用简化信息", e);
            return String.format("App: %s, Port: %s, NodeId: %s, RegisteredAt: %s",
                    applicationName, serverPort, nodeId, LocalDateTime.now());
        }
    }

    /**
     * 获取本地IP地址
     */
    private String getLocalIpAddress() {
        try {
            // 优先获取非回环的IPv4地址
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress() &&
                        !address.isLinkLocalAddress() &&
                        address.isSiteLocalAddress() &&
                        address.getHostAddress().indexOf(':') == -1) { // IPv4
                        return address.getHostAddress();
                    }
                }
            }

            // 如果没有找到合适的地址，返回本地主机地址
            return InetAddress.getLocalHost().getHostAddress();

        } catch (Exception e) {
            log.debug("获取本地IP地址失败", e);
            return null;
        }
    }
}
