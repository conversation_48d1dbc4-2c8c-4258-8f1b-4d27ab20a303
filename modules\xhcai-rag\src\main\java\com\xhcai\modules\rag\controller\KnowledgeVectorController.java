package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.rag.dto.KnowledgeDocumentDTO;
import com.xhcai.modules.rag.dto.KnowledgeVectorQueryDTO;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.service.vector.IVectorStoreProcessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 知识库中文档的各种操作，分段，向量化
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/knowledge/document")
@Tag(name = "知识库的向量操作", description = "知识库从向量数据库的相关接口")
public class KnowledgeVectorController {
    @Autowired
    private List<IVectorStoreProcessor> vectorStoreProcessors;

    @PostMapping("/vector/list")
    @Operation(summary = "知识库内容的向量库查询")
    public Result<KnowledgeSegmentConfig> listVector(@RequestBody KnowledgeVectorQueryDTO knowledgeVectorQueryDTO) {
//        try {
//            KnowledgeSegmentConfig config = knowledgeConfigService.getSegmentConfig();
//            return Result.success(config);
//        } catch (Exception e) {
//            log.error("获取文件分段配置失败", e);
//            return Result.error("获取文件分段配置失败：" + e.getMessage());
//        }
        return null;
    }

    @PostMapping("/embedding")
    @Operation(summary = "按文档进行内容的向量化")
    public Result<KnowledgeSegmentConfig> embeddingVector(@Valid @RequestBody KnowledgeDocumentDTO knowledgeVectorEmbeddingDTO) {

        return null;
    }
}
