package com.xhcai.modules.agent.controller.v1;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.agent.dto.AgentQueryDTO;
import com.xhcai.modules.agent.service.IAgentService;
import com.xhcai.modules.agent.vo.AgentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 智能体控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "智能体管理", description = "智能体的创建、配置、管理等功能")
@RestController
@RequestMapping("/v1/agents")
public class AgentOpenApi {

    @Autowired
    private IAgentService agentService;

    @RequiresApiKey(
            allowTokenAuth = false,
            targetTypes = {"agent"},
            message = "分页查询智能体列表"
    )
    @Operation(summary = "分页查询智能体列表", description = "根据条件分页查询智能体列表")
    @PostMapping("/page")
    @RequiresPermissions("agent:list")
    public Result<PageResult<AgentVO>> getAgentPage(@Valid @RequestBody AgentQueryDTO queryDTO) {
        PageResult<AgentVO> result = agentService.getAgentPage(queryDTO);
        return Result.success(result);
    }

    @RequiresApiKey(
            allowTokenAuth = false,
            targetTypes = {"agent"},
            message = "查询智能体详情"
    )
    @Operation(summary = "查询智能体详情", description = "根据ID查询智能体详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("agent:detail")
    public Result<AgentVO> getAgentById(@Parameter(description = "智能体ID") @PathVariable String id) {
        AgentVO agent = agentService.getAgentById(id);
        return Result.success(agent);
    }
}
