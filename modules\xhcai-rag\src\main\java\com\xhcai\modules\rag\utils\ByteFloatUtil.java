package com.xhcai.modules.rag.utils;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;


public class ByteFloatUtil {
    /**
     * 将float数组转换为字节数组（大端序）
     */
    public static byte[] floatArrayToByteArray(float[] array) {
        // 空/空数组处理：返回空字节数组，避免NPE
        if (array == null || array.length == 0) {
            return new byte[0];
        }
        // 使用Java默认的大端序（与byteArrayToFloatArray保持一致）
        ByteBuffer buffer = ByteBuffer.allocate(array.length * Float.BYTES);
        for (float v : array) {
            buffer.putFloat(v);
        }
        return buffer.array();
    }

    /**
     * 将字节数组转换为float数组（大端序）
     */
    public static float[] byteArrayToFloatArray(byte[] bytes) {
        // 空/空数组处理：返回空float数组
        if (bytes == null || bytes.length == 0) {
            return new float[0];
        }
        // 校验长度是否为4的倍数（一个float占4字节）
        if (bytes.length % Float.BYTES != 0) {
            throw new IllegalArgumentException("字节数组长度必须是4的倍数");
        }
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        int len = bytes.length / Float.BYTES;
        float[] array = new float[len];
        for (int i = 0; i < len; i++) {
            array[i] = buffer.getFloat();
        }
        return array;
    }

    /**
     * 将float数组转换为字节数组（小端序）
     */
    public static byte[] floatArrayToByteArrayLE(float[] array) {
        // 空/空数组处理：返回空字节数组，避免NPE
        if (array == null || array.length == 0) {
            return new byte[0];
        }
        ByteBuffer buffer = ByteBuffer.allocate(array.length * Float.BYTES).order(ByteOrder.LITTLE_ENDIAN);
        for (float v : array) {
            buffer.putFloat(v);
        }
        return buffer.array();
    }

    /**
     * 将字节数组转换为float数组（小端序）
     */
    public static float[] byteArrayToFloatArrayLE(byte[] bytes) {
        // 空/空数组处理：返回空float数组
        if (bytes == null || bytes.length == 0) {
            return new float[0];
        }
        // 校验长度必须是4的倍数
        if (bytes.length % Float.BYTES != 0) {
            throw new IllegalArgumentException("字节数组长度必须是4的倍数");
        }
        ByteBuffer buffer = ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN);
        int len = bytes.length / Float.BYTES;
        float[] array = new float[len];
        for (int i = 0; i < len; i++) {
            array[i] = buffer.getFloat();
        }
        return array;
    }
}
