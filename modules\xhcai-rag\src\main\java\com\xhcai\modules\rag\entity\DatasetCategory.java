package com.xhcai.modules.rag.entity;

import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 文档分类实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档分类实体")
@Entity
@Data
@Table(name = "dataset_categories")
@TableName(value = "dataset_categories", autoResultMap = true)
public class DatasetCategory extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID", example = "dataset123")
    @Column(name = "dataset_id", nullable = false, length = 32)
    @TableField("dataset_id")
    private String datasetId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "产品文档")
    @Column(name = "name", nullable = false, length = 100)
    @TableField("name")
    private String name;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述", example = "产品相关的文档资料")
    @Column(name = "description", length = 500)
    @TableField("description")
    private String description;

    /**
     * 父分类ID
     */
    @Schema(description = "父分类ID", example = "parent123")
    @Column(name = "parent_id", length = 32)
    @TableField("parent_id")
    private String parentId;


    /**
     * 分类层级
     */
    @Schema(description = "分类层级", example = "1")
    @Column(name = "level", nullable = false)
    @TableField("level")
    private Integer level = 0;

    /**
     * 排序号
     */
    @Schema(description = "排序号", example = "1")
    @Column(name = "sort_order", nullable = false)
    @TableField("sort_order")
    private Integer sortOrder = 0;

    /**
     * 文件数量
     */
    @Schema(description = "文件数量", example = "15")
    @Column(name = "file_count", nullable = false)
    @TableField("file_count")
    private Integer fileCount = 0;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    @Column(name = "enabled", nullable = false)
    @TableField("enabled")
    private Boolean enabled = true;
}
