package com.xhcai.modules.rag.plugins.rabbitmq.util;

import com.xhcai.modules.rag.plugins.rabbitmq.model.MessageType;
import com.xhcai.modules.rag.plugins.rabbitmq.model.RabbitMQMessage;
import com.xhcai.modules.rag.plugins.rabbitmq.producer.RabbitMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * RabbitMQ工具类
 * 提供便捷的消息发送和管理方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "xhcai.plugin.types.queue.config", name = "type", havingValue = "rabbitmq")
public class RabbitMQUtil {

    @Autowired
    private RabbitMQProducer rabbitMQProducer;

    /**
     * 发送简单消息
     *
     * @param messageType 消息类型
     * @param payload     消息内容
     * @param tenantId    租户ID
     * @param userId      用户ID
     */
    public void sendSimpleMessage(MessageType messageType, Object payload, String tenantId, String userId) {
        RabbitMQMessage message = createMessage(messageType, payload, tenantId, userId);
        rabbitMQProducer.sendMessage(message);
        log.info("简单消息发送成功: type={}, tenantId={}, userId={}", messageType, tenantId, userId);
    }

    /**
     * 发送带属性的消息
     *
     * @param messageType 消息类型
     * @param payload     消息内容
     * @param properties  消息属性
     * @param tenantId    租户ID
     * @param userId      用户ID
     */
    public void sendMessageWithProperties(MessageType messageType, Object payload, 
                                          Map<String, Object> properties, String tenantId, String userId) {
        RabbitMQMessage message = createMessage(messageType, payload, tenantId, userId);
        message.setProperties(properties);
        rabbitMQProducer.sendMessage(message);
        log.info("带属性消息发送成功: type={}, tenantId={}, userId={}", messageType, tenantId, userId);
    }

    /**
     * 发送优先级消息
     *
     * @param messageType 消息类型
     * @param payload     消息内容
     * @param priority    优先级（1-10，数字越大优先级越高）
     * @param tenantId    租户ID
     * @param userId      用户ID
     */
    public void sendPriorityMessage(MessageType messageType, Object payload, int priority, 
                                    String tenantId, String userId) {
        RabbitMQMessage message = createMessage(messageType, payload, tenantId, userId);
        rabbitMQProducer.sendPriorityMessage(message, priority);
        log.info("优先级消息发送成功: type={}, priority={}, tenantId={}, userId={}", 
                messageType, priority, tenantId, userId);
    }

    /**
     * 发送延迟消息
     *
     * @param messageType 消息类型
     * @param payload     消息内容
     * @param delayTime   延迟时间（毫秒）
     * @param tenantId    租户ID
     * @param userId      用户ID
     */
    public void sendDelayMessage(MessageType messageType, Object payload, long delayTime, 
                                 String tenantId, String userId) {
        RabbitMQMessage message = createMessage(messageType, payload, tenantId, userId);
        rabbitMQProducer.sendDelayMessage(message, delayTime);
        log.info("延迟消息发送成功: type={}, delayTime={}ms, tenantId={}, userId={}", 
                messageType, delayTime, tenantId, userId);
    }

    /**
     * 批量发送消息
     *
     * @param messageType 消息类型
     * @param payloads    消息内容列表
     * @param tenantId    租户ID
     * @param userId      用户ID
     */
    public void sendBatchMessages(MessageType messageType, List<Object> payloads, 
                                  String tenantId, String userId) {
        List<RabbitMQMessage> messages = payloads.stream()
                .map(payload -> createMessage(messageType, payload, tenantId, userId))
                .collect(Collectors.toList());
        
        rabbitMQProducer.sendBatchMessages(messages);
        log.info("批量消息发送成功: type={}, count={}, tenantId={}, userId={}", 
                messageType, payloads.size(), tenantId, userId);
    }

    /**
     * 发送通知消息
     *
     * @param title    通知标题
     * @param content  通知内容
     * @param type     通知类型
     * @param tenantId 租户ID
     * @param userId   用户ID
     */
    public void sendNotification(String title, String content, String type, String tenantId, String userId) {
        Map<String, Object> notificationData = new HashMap<>();
        notificationData.put("title", title);
        notificationData.put("content", content);
        notificationData.put("type", type);
        notificationData.put("timestamp", LocalDateTime.now());
        
        rabbitMQProducer.sendNotificationMessage(notificationData, tenantId, userId);
        log.info("通知消息发送成功: title={}, type={}, tenantId={}, userId={}", title, type, tenantId, userId);
    }

    /**
     * 发送系统消息
     *
     * @param systemEvent 系统事件
     * @param data        事件数据
     * @param tenantId    租户ID
     */
    public void sendSystemMessage(String systemEvent, Object data, String tenantId) {
        Map<String, Object> systemData = new HashMap<>();
        systemData.put("event", systemEvent);
        systemData.put("data", data);
        systemData.put("timestamp", LocalDateTime.now());
        
        RabbitMQMessage message = createMessage(MessageType.SYSTEM, systemData, tenantId, "system");
        rabbitMQProducer.sendMessage(message);
        log.info("系统消息发送成功: event={}, tenantId={}", systemEvent, tenantId);
    }

    /**
     * 发送错误消息
     *
     * @param errorCode    错误代码
     * @param errorMessage 错误消息
     * @param errorData    错误数据
     * @param tenantId     租户ID
     * @param userId       用户ID
     */
    public void sendErrorMessage(String errorCode, String errorMessage, Object errorData, 
                                 String tenantId, String userId) {
        Map<String, Object> errorInfo = new HashMap<>();
        errorInfo.put("errorCode", errorCode);
        errorInfo.put("errorMessage", errorMessage);
        errorInfo.put("errorData", errorData);
        errorInfo.put("timestamp", LocalDateTime.now());
        
        RabbitMQMessage message = createMessage(MessageType.ERROR, errorInfo, tenantId, userId);
        rabbitMQProducer.sendMessage(message);
        log.info("错误消息发送成功: errorCode={}, tenantId={}, userId={}", errorCode, tenantId, userId);
    }

    /**
     * 发送健康检查消息
     */
    public void sendHealthCheckMessage() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("timestamp", LocalDateTime.now());
        healthData.put("source", "rag-service");
        healthData.put("status", "healthy");
        
        RabbitMQMessage message = createMessage(MessageType.HEALTH_CHECK, healthData, "system", "system");
        rabbitMQProducer.sendMessage(message);
        log.info("健康检查消息发送成功");
    }

    /**
     * 创建消息对象
     */
    private RabbitMQMessage createMessage(MessageType messageType, Object payload, String tenantId, String userId) {
        return RabbitMQMessage.builder()
                .messageId(generateMessageId())
                .messageType(messageType)
                .payload(payload)
                .sender("rag-service")
                .priority(5)
                .retryCount(2)
                .maxRetryCount(3)
                .createTime(LocalDateTime.now())
                .expireTime(LocalDateTime.now().plusHours(1))
                .status(RabbitMQMessage.MessageStatus.PENDING)
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 创建业务键
     */
    private String createBusinessKey(String prefix, String id) {
        return prefix + "_" + id;
    }

    /**
     * 验证租户ID
     */
    private boolean isValidTenantId(String tenantId) {
        return tenantId != null && !tenantId.trim().isEmpty();
    }

    /**
     * 验证用户ID
     */
    private boolean isValidUserId(String userId) {
        return userId != null && !userId.trim().isEmpty();
    }

    /**
     * 获取当前时间戳
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
}
