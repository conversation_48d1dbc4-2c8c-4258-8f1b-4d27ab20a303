package com.xhcai.common.security.service;

import java.time.LocalDateTime;

/**
 * API密钥认证服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ApiKeyAuthenticationService {

    /**
     * 生成API密钥
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param tenantId 租户ID
     * @param deptId 部门ID
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param expiresAt 过期天数
     * @return JWT格式的API密钥
     */
    String generateApiKey(String userId, String username, String tenantId, String deptId,
                         String targetType, String targetId, LocalDateTime expiresAt);

    /**
     * 生成组合API密钥
     * @param userId
     * @param username
     * @param tenantId
     * @param deptId
     * @param targetType
     * @param targetId
     * @return
     */
    String compositeApiKey(String userId, String username, String tenantId, String deptId,
                         String targetType, String targetId);

    /**
     * 生成密钥哈希值
     *
     * @param apiKey 原始API密钥
     * @return SHA-256哈希值
     */
    String generateKeyHash(String apiKey);
}