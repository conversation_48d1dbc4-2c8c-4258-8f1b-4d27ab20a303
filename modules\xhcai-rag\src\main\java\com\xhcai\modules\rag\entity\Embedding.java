package com.xhcai.modules.rag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 嵌入向量实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "embeddings")
@TableName("embeddings")
public class Embedding extends BaseEntity {
    /**
     * 内容哈希值
     */
    @Column(name = "hash", nullable = false, length = 64)
    @TableField("hash")
    private String hash;

    /**
     * 嵌入向量内容
     */
    @Column(name = "embedding", nullable = false, columnDefinition = "bytea")
    @TableField("embedding")
    private byte[] embedding;

    /**
     * 模型id，用来关联model.id
     */
    @Column(name = "model_id", nullable = false, length = 36)
    @TableField("model_id")
    private String modelId;
}
