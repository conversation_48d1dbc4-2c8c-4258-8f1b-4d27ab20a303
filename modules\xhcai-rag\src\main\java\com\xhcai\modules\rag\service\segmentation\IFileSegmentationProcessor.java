package com.xhcai.modules.rag.service.segmentation;

import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;

import java.io.InputStream;
import java.util.List;

/**
 * 文件分段处理器接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IFileSegmentationProcessor {

    /**
     * 支持的文件类型
     *
     * @return 支持的文件扩展名列表
     */
    List<String> getSupportedFileTypes();

    /**
     * 是否支持该文件类型
     *
     * @param fileExtension 文件扩展名
     * @return 是否支持
     */
    boolean supports(String fileExtension);

    /**
     * 处理文件分段
     *
     * @param document    文档信息
     * @param inputStream 文件输入流
     * @return 分段结果列表
     * @throws Exception 处理异常
     */
    List<SegmentResult> processSegmentation(Document document, InputStream inputStream) throws Exception;

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getProcessorName();

    /**
     * 获取处理器优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    int getPriority();
}
