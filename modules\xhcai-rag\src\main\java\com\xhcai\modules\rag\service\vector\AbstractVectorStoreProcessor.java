package com.xhcai.modules.rag.service.vector;


public abstract class AbstractVectorStoreProcessor implements IVectorStoreProcessor{
    @Override
    public boolean supports(String vectorDbType) {
        return  getSupportedVectorDbType().equals(vectorDbType);
    }

    /**
     * 拼接ip和端口
     * @param ip
     * @param port
     * @return
     */
    protected String concatIpPort(String ip, Integer port){
        return ip + ":" + port;
    }

    /**
     * weaviate的class名称
     * 格式：必须以大写或小写字母（A-Z, a-z）开头。
     * 允许的字符：后续字符可以包含：
     * 字母（A-Z, a-z）
     * 数字（0-9）
     * 下划线 （_）
     * 禁止的字符：
     * 连字符 （-） → 这就是您的问题所在
     * 空格 （）
     * 点号 （.）
     * 其他任何特殊符号（如 @, #, $, % 等）
     * elasticsearch的索引名称
     * @param datasetId
     * @return
     */
    protected String getIndexName(String datasetId){
        return "Knowlege_" + datasetId;
    }
}
