package com.xhcai.modules.rag.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.DatasetCategoryCreateDTO;
import com.xhcai.modules.rag.dto.DatasetCategoryUpdateDTO;
import com.xhcai.modules.rag.entity.DatasetCategory;
import com.xhcai.modules.rag.mapper.DatasetCategoryMapper;
import com.xhcai.modules.rag.service.IDocumentCategoryService;
import com.xhcai.modules.rag.vo.DatasetCategoryVO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档分类服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor
public class DocumentCategoryServiceImpl extends ServiceImpl<DatasetCategoryMapper, DatasetCategory> implements IDocumentCategoryService {

    private final DatasetCategoryMapper datasetCategoryMapper;

    @Override
    public List<DatasetCategoryVO> getCategoryTree() {
        return getCategoryTree(null);
    }

    @Override
    public List<DatasetCategoryVO> getCategoryTree(String datasetId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 使用Mapper方法获取带文件数统计的分类树
        List<DatasetCategoryVO> categoryTree = datasetCategoryMapper.selectCategoryTreeWithFileCount(tenantId, datasetId);

        // 构建树形结构
        return buildCategoryTreeFromVO(categoryTree, null);
    }

    @Override
    public List<DatasetCategoryVO> getCategoriesByParentId(String parentId) {
        return getCategoriesByParentId(parentId, null);
    }

    @Override
    public List<DatasetCategoryVO> getCategoriesByParentId(String parentId, String datasetId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        List<DatasetCategory> categories = datasetCategoryMapper.selectByParentId(tenantId, datasetId, parentId);

        return categories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DatasetCategoryVO createCategory(DatasetCategoryCreateDTO createDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        // 检查名称是否重复
        if (existsCategoryName(createDTO.getName(), createDTO.getParentId(), null, createDTO.getDatasetId())) {
            throw new BusinessException("分类名称已存在");
        }

        // 计算层级
        int level = 0;
        if (StringUtils.hasText(createDTO.getParentId())) {
            DatasetCategory parentCategory = getById(createDTO.getParentId());
            if (parentCategory == null) {
                throw new BusinessException("父分类不存在");
            }
            level = parentCategory.getLevel() + 1;
            if (level > 2) {
                throw new BusinessException("分类层级不能超过3级");
            }
        }

        // 获取排序号
        Integer sortOrder = createDTO.getSortOrder();
        if (sortOrder == null) {
            sortOrder = datasetCategoryMapper.selectMaxSortOrder(tenantId, createDTO.getParentId()) + 1;
        }

        // 创建分类
        DatasetCategory category = new DatasetCategory();
        BeanUtils.copyProperties(createDTO, category);
        category.setLevel(level);
        category.setSortOrder(sortOrder);
        category.setFileCount(0);
        category.setEnabled(true);
        category.setTenantId(tenantId);
        category.setCreateBy(currentUserId);

        save(category);

        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DatasetCategoryVO updateCategory(String categoryId, DatasetCategoryUpdateDTO updateDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        DatasetCategory category = getById(categoryId);
        if (category == null || !tenantId.equals(category.getTenantId())) {
            throw new BusinessException("分类不存在");
        }

        // 检查名称是否重复
        if (existsCategoryName(updateDTO.getName(), category.getParentId(), categoryId, category.getDatasetId())) {
            throw new BusinessException("分类名称已存在");
        }

        // 更新分类
        category.setName(updateDTO.getName());
        category.setDescription(updateDTO.getDescription());
        if (updateDTO.getSortOrder() != null) {
            category.setSortOrder(updateDTO.getSortOrder());
        }
        if (updateDTO.getEnabled() != null) {
            category.setEnabled(updateDTO.getEnabled());
        }
        category.setUpdateBy(currentUserId);

        updateById(category);

        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        DatasetCategory category = getById(categoryId);
        if (category == null || !tenantId.equals(category.getTenantId())) {
            throw new BusinessException("分类不存在");
        }

        // 检查是否有子分类
        LambdaQueryWrapper<DatasetCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DatasetCategory::getTenantId, tenantId)
                .eq(DatasetCategory::getParentId, categoryId)
                .eq(DatasetCategory::getDeleted, 0);

        long childCount = count(wrapper);
        if (childCount > 0) {
            throw new BusinessException("存在子分类，无法删除");
        }

        // 检查是否有文档
        if (category.getFileCount() > 0) {
            throw new BusinessException("分类下存在文档，无法删除");
        }

        // 软删除
        category.setDeleted(1);
        category.setUpdateBy(currentUserId);
        updateById(category);
    }

    @Override
    public DatasetCategoryVO getCategoryById(String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        DatasetCategory category = getById(categoryId);
        if (category == null || !tenantId.equals(category.getTenantId()) || category.getDeleted() == 1) {
            return null;
        }

        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCategoryFileCount(String categoryId, Integer fileCount) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        datasetCategoryMapper.updateFileCount(tenantId, categoryId, fileCount);
    }

    @Override
    public boolean existsCategoryName(String name, String parentId, String excludeId) {
        return existsCategoryName(name, parentId, excludeId, null);
    }

    @Override
    public boolean existsCategoryName(String name, String parentId, String excludeId, String datasetId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        LambdaQueryWrapper<DatasetCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DatasetCategory::getTenantId, tenantId)
                .eq(DatasetCategory::getName, name)
                .eq(DatasetCategory::getDeleted, 0);

        if (StringUtils.hasText(datasetId)) {
            wrapper.eq(DatasetCategory::getDatasetId, datasetId);
        }

        if (StringUtils.hasText(parentId)) {
            wrapper.eq(DatasetCategory::getParentId, parentId);
        } else {
            wrapper.isNull(DatasetCategory::getParentId);
        }

        if (StringUtils.hasText(excludeId)) {
            wrapper.ne(DatasetCategory::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public List<String> getCategoryAndChildrenIds(String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        return datasetCategoryMapper.selectCategoryAndChildrenIds(tenantId, categoryId);
    }

    /**
     * 构建分类树
     */
    private List<DatasetCategoryVO> buildCategoryTree(List<DatasetCategory> allCategories, String parentId) {
        List<DatasetCategoryVO> result = new ArrayList<>();

        // 按父ID分组
        Map<String, List<DatasetCategory>> categoryMap = allCategories.stream()
                .collect(Collectors.groupingBy(category
                        -> category.getParentId() == null ? "root" : category.getParentId()));

        // 获取指定父ID的分类
        String mapKey = parentId == null ? "root" : parentId;
        List<DatasetCategory> categories = categoryMap.getOrDefault(mapKey, new ArrayList<>());

        for (DatasetCategory category : categories) {
            DatasetCategoryVO vo = convertToVO(category);

            // 递归获取子分类
            List<DatasetCategoryVO> children = buildCategoryTree(allCategories, category.getId());
            vo.setChildren(children);

            result.add(vo);
        }

        return result;
    }

    /**
     * 构建分类树（从VO列表）
     */
    private List<DatasetCategoryVO> buildCategoryTreeFromVO(List<DatasetCategoryVO> allCategories, String parentId) {
        List<DatasetCategoryVO> result = new ArrayList<>();

        // 按父ID分组
        Map<String, List<DatasetCategoryVO>> categoryMap = allCategories.stream()
                .collect(Collectors.groupingBy(category
                        -> category.getParentId() == null ? "root" : category.getParentId()));

        // 获取指定父ID的分类
        String mapKey = parentId == null ? "root" : parentId;
        List<DatasetCategoryVO> categories = categoryMap.getOrDefault(mapKey, new ArrayList<>());

        for (DatasetCategoryVO category : categories) {
            // 递归获取子分类
            List<DatasetCategoryVO> children = buildCategoryTreeFromVO(allCategories, category.getId());
            category.setChildren(children);

            result.add(category);
        }

        return result;
    }

    /**
     * 转换为VO
     */
    private DatasetCategoryVO convertToVO(DatasetCategory category) {
        DatasetCategoryVO vo = new DatasetCategoryVO();
        BeanUtils.copyProperties(category, vo);
        return vo;
    }
}
