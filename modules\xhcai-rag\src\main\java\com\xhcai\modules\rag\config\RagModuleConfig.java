package com.xhcai.modules.rag.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RAG模块配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableConfigurationProperties(RagProperties.class)
public class RagModuleConfig {

    private static final Logger log = LoggerFactory.getLogger(RagModuleConfig.class);

    /**
     * RAG模块API分组配置
     */
    @Bean
    public GroupedOpenApi ragApi() {
        return GroupedOpenApi.builder()
                .group("rag")
                .displayName("RAG知识库管理")
                .pathsToMatch("/api/rag/**")
                .build();
    }

    /**
     * 知识库模块API开放清单
     */
    @Bean
    public GroupedOpenApi datasetsOpenApi() {
        return GroupedOpenApi.builder()
                .group("datasetsOpenApi")
                .displayName("知识库OpenApi")
                .pathsToMatch("/v1/datasets/**")
                .build();
    }
}
