package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 文档分类更新DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档分类更新DTO")
@Data
public class DatasetCategoryUpdateDTO {

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "产品文档")
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String name;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述", example = "产品相关的文档资料")
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    private String description;

    /**
     * 排序号
     */
    @Schema(description = "排序号", example = "1")
    private Integer sortOrder;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
}
