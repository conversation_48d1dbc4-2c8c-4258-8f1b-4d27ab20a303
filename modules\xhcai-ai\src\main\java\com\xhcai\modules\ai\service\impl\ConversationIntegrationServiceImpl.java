package com.xhcai.modules.ai.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xhcai.common.core.utils.TimeUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.agent.entity.Agent;
import com.xhcai.modules.agent.entity.AgentConversation;
import com.xhcai.modules.agent.entity.AgentMessage;
import com.xhcai.modules.agent.mapper.AgentConversationMapper;
import com.xhcai.modules.agent.mapper.AgentMapper;
import com.xhcai.modules.agent.mapper.AgentMessageMapper;
import com.xhcai.modules.ai.dto.ConversationInfo;
import com.xhcai.modules.ai.dto.DifyStreamMetadata;
import com.xhcai.modules.ai.entity.AiChatRecord;
import com.xhcai.modules.ai.service.IConversationIntegrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 会话集成服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源，因为agent相关表在主数据源中
public class ConversationIntegrationServiceImpl implements IConversationIntegrationService {

    private static final Logger log = LoggerFactory.getLogger(ConversationIntegrationServiceImpl.class);

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private AgentConversationMapper conversationMapper;

    @Autowired
    private AgentMessageMapper messageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)

    public String createOrGetAgentConversation(ConversationInfo con) {


        // 首先尝试根据sessionId查找现有会话
        String existingConversationId = findAgentConversationId(con.getId());
        if (StringUtils.hasText(existingConversationId)) {
            log.info("找到现有Agent会话: conversationId={}", existingConversationId);
            return existingConversationId;
        }

        // 创建新的Agent会话
        AgentConversation conversation = new AgentConversation();
        conversation.setId(UUID.randomUUID().toString().replace("-", ""));
        conversation.setAgentId(con.getAgentId());
        conversation.setSessionId(con.getId());


        conversationMapper.insert(conversation);
        
        log.info("创建新的Agent会话: conversationId={}, agentId={}, sessionId={}", 
                conversation.getId(), con.getAgentId(), con.getId());
        
        return conversation.getId();
    }

    public String updateAgentConversation(ConversationInfo con) {

        String userId = SecurityUtils.getCurrentUserId();

        // 首先尝试根据sessionId查找现有会话
        String existingConversationId = findAgentConversationId(con.getId());
        // 创建新的Agent会话
        AgentConversation conversation = new AgentConversation();
        if (StringUtils.hasText(existingConversationId)) {
            conversation.setId(existingConversationId);
        }else{
            conversation.setId(UUID.randomUUID().toString().replace("-", ""));
        }
        conversation.setAgentId(con.getAgentId());
        conversation.setUserId(userId);
        conversation.setCreateBy(userId);
        conversation.setSessionId(con.getId());
        conversation.setTitle(con.getName());
        conversation.setStatus(con.getStatus());
        conversation.setMessageCount(0);
        // 使用工具方法转换时间戳为北京时间
        conversation.setStartedAt(TimeUtils.convertTimestampToBeijingTime(con.getCreatedAt()));
        conversation.setLastActivityAt(TimeUtils.convertTimestampToBeijingTime(con.getUpdatedAt()));
        conversation.setTotalTokens(0L);
        conversation.setInputTokens(0L);
        conversation.setOutputTokens(0L);
        conversation.setCost(0L);

        conversationMapper.insertOrUpdate(conversation);

        log.info("更新Agent会话: conversationId={}, agentId={}, sessionId={}",
                conversation.getId(),con.getAgentId(), con.getId());

        return conversation.getId();
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public String saveUserMessage(String conversationId, AiChatRequest request) {
//        if (!StringUtils.hasText(conversationId)) {
//            return null;
//        }
//
//        String tenantId = SecurityUtils.getCurrentTenantId();
//
//        AgentMessage message = new AgentMessage();
//        message.setId(UUID.randomUUID().toString().replace("-", ""));
//        message.setConversationId(conversationId);
//        message.setMessageType("user");
//        message.setContent(request.getQuery());
//        message.setSequenceNumber(messageMapper.getNextSequenceNumber(conversationId, tenantId));
//        message.setStatus("sent");
//        message.setSentAt(LocalDateTime.now());
//        message.setReceivedAt(LocalDateTime.now());
//
//        messageMapper.insert(message);
//
//        log.info("保存用户消息到Agent系统: messageId={}, conversationId={}",
//                message.getId(), conversationId);
//
//        return message.getId();
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveAssistantMessage(String conversationId, String aiResponse, AiChatRecord chatRecord) {
        if (!StringUtils.hasText(conversationId) || !StringUtils.hasText(aiResponse)) {
            return null;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        
        AgentMessage message = new AgentMessage();
        message.setId(UUID.randomUUID().toString().replace("-", ""));
        message.setConversationId(conversationId);
        message.setMessageType("assistant");
        message.setContent(aiResponse);
        message.setParentMessageId("");
        message.setStatus("sent");
        message.setSentAt(LocalDateTime.now());
        message.setReceivedAt(LocalDateTime.now());
        
        // 设置处理时间和token统计
        if (chatRecord != null) {
            message.setProcessingTime(chatRecord.getCostTime());
            message.setTokens(chatRecord.getTokensUsed());
            // 可以根据需要设置inputTokens和outputTokens
        }

        messageMapper.insert(message);
        
        log.info("保存AI回复消息到Agent系统: messageId={}, conversationId={}", 
                message.getId(), conversationId);
        
        return message.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateConversationStats(String conversationId, AiChatRecord chatRecord) {
        if (!StringUtils.hasText(conversationId)) {
            return;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        
        // 获取消息统计
        Integer messageCount = messageMapper.countByConversationId(conversationId, tenantId);
        
        // 更新会话统计
        conversationMapper.updateMessageStats(conversationId, messageCount, LocalDateTime.now());
        
        // 如果有token统计，也可以更新
        if (chatRecord != null && chatRecord.getTokensUsed() != null) {
            // 这里可以累加token统计
            log.info("更新会话统计: conversationId={}, messageCount={}, tokens={}", 
                    conversationId, messageCount, chatRecord.getTokensUsed());
        }
    }

    @Override
    public String findAgentConversationId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            return null;
        }

        // 根据sessionId查找Agent会话
        // 注意：不需要传递tenantId，多租户插件会自动处理
        AgentConversation conversation = conversationMapper.selectBySessionId(sessionId);
        if (conversation != null) {
            return conversation.getId();
        }
        
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncChatRecordToAgent(AiChatRecord chatRecord) {
        if (chatRecord == null || !StringUtils.hasText(chatRecord.getAppId())) {
            return;
        }

        try {
            // 构建AiChatRequest对象
            ConversationInfo con= new ConversationInfo();
            con.setId(chatRecord.getSessionId());

            // 创建或获取Agent会话
            String conversationId = createOrGetAgentConversation(con);
            if (StringUtils.hasText(conversationId)) {

                saveAssistantMessage(conversationId, chatRecord.getAiResponse(), chatRecord);

                // 更新会话统计
                updateConversationStats(conversationId, chatRecord);

                log.info("同步AI聊天记录到Agent系统完成: chatRecordId={}, conversationId={}",
                        chatRecord.getId(), conversationId);
            }
        } catch (Exception e) {
            log.error("同步AI聊天记录到Agent系统失败: chatRecordId={}", chatRecord.getId(), e);
        }
    }

    /**
     * 根据appId查找对应的Agent
     */
    private Agent findAgentByAppId(String appId) {
        if (!StringUtils.hasText(appId)) {
            return null;
        }

        // 根据external_agent_id字段查找Agent
        // 注意：不需要手动添加tenant_id条件，多租户插件会自动处理
        LambdaQueryWrapper<Agent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Agent::getExternalAgentId, appId)
               .eq(Agent::getDeleted, 0); // deleted字段是Integer类型，0表示未删除

        return agentMapper.selectOne(wrapper);
    }

    @Override
    public Integer getNextSequenceNumber(String conversationId) {
        if (!StringUtils.hasText(conversationId)) {
            return 1;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return messageMapper.getNextSequenceNumber(conversationId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveUserMessage(String conversationId, String message, Integer sequenceNumber, String messageId) {
        if (!StringUtils.hasText(conversationId) || !StringUtils.hasText(message)) {
            return null;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();

        AgentMessage userMessage = new AgentMessage();
        userMessage.setId(messageId);
        userMessage.setConversationId(conversationId);
        userMessage.setMessageType("user");
        userMessage.setContent(message);
        userMessage.setSequenceNumber(sequenceNumber);
        userMessage.setStatus("sent");
        userMessage.setSentAt(LocalDateTime.now());
        userMessage.setReceivedAt(LocalDateTime.now());

        messageMapper.insert(userMessage);

        log.info("保存用户消息到Agent系统: messageId={}, conversationId={}", messageId, conversationId);

        return messageId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveErrorMessage(String conversationId, String errorMessage, Integer sequenceNumber,
                                  String messageId, String parentMessageId, long costTime) {
        if (!StringUtils.hasText(conversationId) || !StringUtils.hasText(errorMessage)) {
            return null;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();

        AgentMessage message = new AgentMessage();
        message.setId(messageId);
        message.setConversationId(conversationId);
        message.setMessageType("assistant");
        message.setContent("处理失败: " + errorMessage);
        message.setSequenceNumber(sequenceNumber);
        message.setParentMessageId(parentMessageId);
        message.setStatus("failed");
        message.setSentAt(LocalDateTime.now());
        message.setReceivedAt(LocalDateTime.now());
        message.setProcessingTime(costTime);
        message.setErrorMessage(errorMessage);

        messageMapper.insert(message);

        log.info("保存错误消息到Agent系统: messageId={}, conversationId={}", messageId, conversationId);

        return messageId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createChatMessageRecord(String conversationId, String userMessage, Object requestDTO,
                                         Integer sequenceNumber, String messageId) {
        if (!StringUtils.hasText(conversationId) || !StringUtils.hasText(userMessage)) {
            return null;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();

        AgentMessage message = new AgentMessage();
        message.setId(messageId);
        message.setConversationId(conversationId);
        message.setMessageType("assistant"); // 问答合并模式，使用assistant类型
        message.setQuery(userMessage); // 用户提问存到query字段
        message.setContent(""); // content字段暂时为空，等待AI回复
        message.setSequenceNumber(sequenceNumber);
        message.setStatus("sending"); // 初始状态为发送中
        message.setSentAt(LocalDateTime.now());

        // 如果有输入参数，序列化存储
        if (requestDTO != null) {
            try {
                // 这里可以根据requestDTO的类型进行不同的处理
                if (requestDTO instanceof com.xhcai.modules.ai.dto.AiChatRequestDTO) {
                    com.xhcai.modules.ai.dto.AiChatRequestDTO dto = (com.xhcai.modules.ai.dto.AiChatRequestDTO) requestDTO;
                    Map<String, Object> inputs = new HashMap<>();
                    inputs.put("messageType", dto.getMessageType());
                    inputs.put("modelName", dto.getModelName());
                    inputs.put("sessionId", dto.getSessionId());
                    message.setInputs(com.xhcai.common.core.utils.JsonUtils.toJsonString(inputs));
                } else if (requestDTO instanceof com.xhcai.modules.ai.dto.AiChatRequest) {
                    com.xhcai.modules.ai.dto.AiChatRequest dto = (com.xhcai.modules.ai.dto.AiChatRequest) requestDTO;
                    Map<String, Object> inputs = new HashMap<>();
                    inputs.put("appId", dto.getAppId());
                    inputs.put("conversationId", dto.getConversationId());
                    inputs.put("messageType", dto.getMessageType());
                    if (dto.getInputs() != null) {
                        inputs.put("originalInputs", dto.getInputs());
                    }
                    message.setInputs(com.xhcai.common.core.utils.JsonUtils.toJsonString(inputs));

                    // 设置外部信息ID（Dify AI回复ID将在后续更新）
                    if (StringUtils.hasText(dto.getAppId())) {
                        message.setExternalInfoId(dto.getAppId());
                    }
                }
            } catch (Exception e) {
                log.warn("序列化输入参数失败", e);
            }
        }

        messageMapper.insert(message);

        log.info("创建聊天消息记录: messageId={}, conversationId={}", messageId, conversationId);

        return messageId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChatMessageWithResponse(String messageId, String aiResponse, long costTime, String errorMessage) {
        if (!StringUtils.hasText(messageId)) {
            return;
        }

        try {
            AgentMessage message = messageMapper.selectById(messageId);
            if (message == null) {
                log.warn("未找到消息记录: messageId={}", messageId);
                return;
            }

            // 更新AI回复内容
            message.setContent(aiResponse);
            message.setProcessingTime(costTime);
            message.setReceivedAt(LocalDateTime.now());

            if (StringUtils.hasText(errorMessage)) {
                message.setStatus("failed");
                message.setErrorMessage(errorMessage);
            } else {
                message.setStatus("sent");
            }

            messageMapper.updateById(message);

            log.info("更新聊天消息记录: messageId={}, status={}, costTime={}ms",
                    messageId, message.getStatus(), costTime);

        } catch (Exception e) {
            log.error("更新聊天消息记录失败: messageId={}", messageId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChatMessageWithDifyResponse(String aiResponse, long costTime,
                                                 String errorMessage, DifyStreamMetadata metadata) {
        if (!StringUtils.hasText(metadata.getMessageId())) {
            return;
        }

        try {
            // 获取或创建对话

            AgentMessage message = new AgentMessage();

            ConversationInfo con = new ConversationInfo();
            con.setId(metadata.getConversationId());
            con.setAgentId(metadata.getAgentId());

//            String conversationId=null;
//            if(!StringUtils.hasText(metadata.getConversationId())){
            String conversationId=createOrGetAgentConversation(con);
//            }else {
//                conversationId=metadata.getConversationId();
//            }



            // 更新AI回复内容
            message.setId(UUID.randomUUID().toString().replace("-", ""));;
            message.setProcessingTime(costTime);
            message.setMessageType("dify");
            message.setQuery(metadata.getQuery());
            message.setContent(aiResponse);
            message.setProcessingTime(costTime);
            message.setReceivedAt(LocalDateTime.now());
            message.setConversationId(conversationId);
            message.setParentMessageId(metadata.getParentMessageId());
            message.setInputs(metadata.getInputs());
            message.setOutputs(metadata.getOutputs());



            if (StringUtils.hasText(errorMessage)) {
                message.setStatus("failed");
                message.setErrorMessage(errorMessage);
            } else {
                message.setStatus("sent");
            }


            // 设置Dify相关字段
            if (StringUtils.hasText(metadata.getMessageId())) {
                message.setExternalInfoId(metadata.getMessageId());
            }

            if (metadata.getPromptTokens() != null) {
                message.setInputTokens(metadata.getPromptTokens());
            }

            if (metadata.getCompletionTokens() != null) {
                message.setOutputTokens(metadata.getCompletionTokens());
            }

            if (metadata.getTotalTokens() != null) {
                message.setTokens(metadata.getTotalTokens());
            }

                log.info("更新Dify元数据: messageId={}, difyMessageId={}, promptTokens={}, completionTokens={}, totalTokens={}",
                        metadata.getMessageId(), metadata.getMessageId(), metadata.getPromptTokens(),
                        metadata.getCompletionTokens(), metadata.getTotalTokens());

            messageMapper.insert(message);

            log.info("更新Dify聊天消息记录: messageId={}, status={}, costTime={}ms",
                    metadata.getMessageId(), message.getStatus(), costTime);

        } catch (Exception e) {
            log.error("更新Dify聊天消息记录失败: messageId={}", metadata.getMessageId(), e);
        }
    }
}
