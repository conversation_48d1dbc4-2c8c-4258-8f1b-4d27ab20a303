package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresAuthentication;
import com.xhcai.modules.dify.dto.conversation.DifyConversationDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyMessageListResponseDTO;
import com.xhcai.modules.dify.service.IDifyConversationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "dify会话记录", description = "dify会话记录相关接口")
@RestController
@RequestMapping("/api/dify/conversations")
public class DifyConversationController {

    @Autowired
    private IDifyConversationService difyConversationService;

    /**
     * 获取智能体会话列表-dify
     */
    @Operation(summary = "获取智能体会话列表")
    @GetMapping("/chat/{appId}")
    @RequiresAuthentication
    public Result<DifyConversationListResponseDTO> getChatConversations(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "页码，默认1") @RequestParam(required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页限制数量，默认10") @RequestParam(required = false, defaultValue = "10") Integer limit,
            @Parameter(description = "开始时间，格式：2025-09-05+00%3A00") @RequestParam(required = false) String start,
            @Parameter(description = "结束时间，格式：2025-09-12+23%3A59") @RequestParam(required = false) String end,
            @Parameter(description = "排序方式，默认-created_at") @RequestParam(required = false, defaultValue = "-created_at") String sortBy,
            @Parameter(description = "标注状态，默认all") @RequestParam(required = false, defaultValue = "all") String annotationStatus) {
        DifyConversationListResponseDTO conversations = difyConversationService.getChatConversations(appId, page, limit, start, end, sortBy, annotationStatus);
        return Result.success(conversations);
    }


    /**
     * 获取智能体会话列表-dify（通过appId获取installedAppId,再获取会话列表）
     */
    @Operation(summary = "获取智能体会话列表")
    @GetMapping("/installed/{appId}")
    @RequiresAuthentication
    public Result<DifyConversationListResponseDTO> getConversations(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "限制数量，默认100") @RequestParam(required = false, defaultValue = "100") Integer limit,
            @Parameter(description = "是否只获取置顶会话，默认false") @RequestParam(required = false, defaultValue = "false") Boolean pinned) {
        DifyConversationListResponseDTO conversations = difyConversationService.getConversations(appId, limit, pinned);
        return Result.success(conversations);
    }


    /**
     * 修改会话名称-dify
     */
    @Operation(summary = "修改会话名称")
    @PostMapping("/conversations/{appId}/{conversationId}/name")
    @RequiresAuthentication
    public Result<DifyConversationDTO> updateConversationName(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "会话ID") @PathVariable String conversationId,
            @Parameter(description = "新的会话名称") @RequestParam String name) {
        DifyConversationDTO conversation = difyConversationService.updateConversationName(appId, conversationId, name);
        return Result.success(conversation);
    }

    /**
     * 删除会话记录-dify
     */
    @Operation(summary = "删除会话记录")
    @GetMapping("/conversations/{appId}/{conversationId}/delete")
    @RequiresAuthentication
    public Result<Void> deleteConversation(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "会话ID") @PathVariable String conversationId) {
        boolean success = difyConversationService.deleteConversation(appId, conversationId);
        return success ? Result.success() : Result.fail("删除会话记录失败");
    }

    /**
     * 获取会话消息列表-dify
     */
    @Operation(summary = "获取会话消息列表")
    @GetMapping("/{appId}/{conversationId}/messages")
    @RequiresAuthentication
    public Result<DifyMessageListResponseDTO> getConversationMessages(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "会话ID") @PathVariable String conversationId,
            @Parameter(description = "限制数量，默认20") @RequestParam(required = false, defaultValue = "20") Integer limit,
            @Parameter(description = "最后一个消息ID（用于分页）") @RequestParam(required = false) String lastId) {
        DifyMessageListResponseDTO messagesResponse = difyConversationService.getConversationMessages(appId, conversationId, limit, lastId);
        return Result.success(messagesResponse);
    }
}
