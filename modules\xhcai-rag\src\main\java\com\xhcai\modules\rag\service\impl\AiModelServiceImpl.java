package com.xhcai.modules.rag.service.impl;

import cn.hutool.core.map.TableMap;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.rag.dto.AiModelCreateDTO;
import com.xhcai.modules.rag.dto.AiModelQueryDTO;
import com.xhcai.modules.rag.dto.AiModelUpdateDTO;
import com.xhcai.modules.rag.entity.AiModel;
import com.xhcai.modules.rag.enums.ModelTypeEnum;
import com.xhcai.modules.rag.mapper.AiModelMapper;
import com.xhcai.modules.rag.service.IAiModelService;
import com.xhcai.modules.rag.vo.AiModelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI模型配置服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master") // 使用主数据源
public class AiModelServiceImpl extends ServiceImpl<AiModelMapper, AiModel> implements IAiModelService {

    /**
     * 嵌入模型, key: models.id    value: EmbeddingModel
     */
    private final Map<String, EmbeddingModel> MAP_EMBEDDING_MODELS = new TableMap<>();

    @Override
    public PageResult<AiModelVO> selectAiModelPage(AiModelQueryDTO queryDTO) {
        // 创建分页对象
        Page<AiModelVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        
        // 执行分页查询
        IPage<AiModelVO> pageResult = baseMapper.selectAiModelPage(page, queryDTO);
        
        // 脱敏API密钥
        if (pageResult.getRecords() != null) {
            pageResult.getRecords().forEach(this::maskApiKey);
        }
        
        // 转换为PageResult
        return PageResult.of(
            pageResult.getRecords(),
            pageResult.getTotal(),
            pageResult.getCurrent(),
            pageResult.getSize()
        );
    }

    @Override
    public List<AiModelVO> selectAiModelList(AiModelQueryDTO queryDTO) {
        List<AiModelVO> list = baseMapper.selectAiModelList(queryDTO);
        
        // 脱敏API密钥
        if (list != null) {
            list.forEach(this::maskApiKey);
        }
        
        return list;
    }

    @Override
    public AiModelVO selectAiModelById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("模型ID不能为空");
        }
        
        AiModelVO modelVO = baseMapper.selectAiModelById(id);
        if (modelVO == null) {
            throw new BusinessException("AI模型不存在");
        }
        
        // 脱敏API密钥
        maskApiKey(modelVO);
        
        return modelVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAiModel(AiModelCreateDTO createDTO) {
        // 检查模型标识是否已存在
        if (existsModelId(createDTO.getModelId(), null)) {
            throw new BusinessException("模型标识已存在");
        }
        
        // 检查模型名称是否已存在
        if (existsModelName(createDTO.getName(), null)) {
            throw new BusinessException("模型名称已存在");
        }
        
        // 创建实体对象
        AiModel aiModel = new AiModel();
        BeanUtils.copyProperties(createDTO, aiModel);
        
        // 设置默认值
        if (!StringUtils.hasText(aiModel.getStatus())) {
            aiModel.setStatus("1");
        }
        boolean result = save(aiModel);
        if (result) {
            log.info("创建AI模型成功: {}", aiModel.getName());
        }
        insUpdDelOpenAiEmbeddingModel(aiModel.getId());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAiModel(AiModelUpdateDTO updateDTO) {
        // 检查模型是否存在
        AiModel existingModel = getById(updateDTO.getId());
        if (existingModel == null) {
            throw new BusinessException("AI模型不存在");
        }
        
        // 检查模型标识是否已存在（排除自己）
        if (existsModelId(updateDTO.getModelId(), updateDTO.getId())) {
            throw new BusinessException("模型标识已存在");
        }
        
        // 检查模型名称是否已存在（排除自己）
        if (existsModelName(updateDTO.getName(), updateDTO.getId())) {
            throw new BusinessException("模型名称已存在");
        }
        
        // 更新实体对象
        AiModel aiModel = new AiModel();
        BeanUtils.copyProperties(updateDTO, aiModel);
        
        boolean result = updateById(aiModel);
        if (result) {
            log.info("更新AI模型成功: {}", aiModel.getName());
        }
        insUpdDelOpenAiEmbeddingModel(updateDTO.getId());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAiModel(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("模型ID不能为空");
        }
        AiModel aiModel = getById(id);
        if (aiModel == null) {
            throw new BusinessException("AI模型不存在");
        }
        boolean result = removeById(id);
        if (result) {
            log.info("删除AI模型成功: {}", aiModel.getName());
        }
        insUpdDelOpenAiEmbeddingModel(id);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteAiModels(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("删除的模型ID不能为空");
        }
        for (String id : ids) {
            boolean b = deleteAiModel(id);
            if (!b) {
                log.error("批量删除AI模型成功，数量: {}", ids.size());
                throw new BusinessException("模型ID不能为空");
            }
        }
        return true;
    }

    @Override
    public boolean existsModelId(String modelId, String excludeId) {
        if (!StringUtils.hasText(modelId)) {
            return false;
        }
        return baseMapper.checkModelIdExists(modelId, excludeId) > 0;
    }

    @Override
    public boolean existsModelName(String name, String excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        return baseMapper.checkModelNameExists(name, excludeId) > 0;
    }

    @Override
    public List<AiModelVO> selectByProvider(String provider) {
        if (!StringUtils.hasText(provider)) {
            throw new BusinessException("提供商不能为空");
        }
        
        List<AiModelVO> list = baseMapper.selectByProvider(provider);
        
        // 脱敏API密钥
        if (list != null) {
            list.forEach(this::maskApiKey);
        }
        
        return list;
    }

    @Override
    public List<AiModelVO> selectByType(String type) {
        if (!StringUtils.hasText(type)) {
            throw new BusinessException("模型类型不能为空");
        }
        
        List<AiModelVO> list = baseMapper.selectByType(type);
        
        // 脱敏API密钥
        if (list != null) {
            list.forEach(this::maskApiKey);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableAiModel(String id) {
        return baseMapper.batchUpdateStatus(List.of(id),"1") > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableAiModel(String id) {
        return baseMapper.batchUpdateStatus(List.of(id),"0") > 0;
    }

    @Override
    public List<Map<String, Object>> countByProvider() {
        return baseMapper.countByProvider();
    }

    @Override
    public List<Map<String, Object>> countByType() {
        return baseMapper.countByType();
    }

    @Override
    public Map<String, Object> testModelConnection(String id) {
        AiModel model = getById(id);
        if (model == null) {
            throw new BusinessException("AI模型不存在");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // TODO: 实现具体的模型连接测试逻辑
            // 这里可以根据不同的提供商实现不同的测试逻辑
            
            result.put("success", true);
            result.put("message", "连接测试成功");
            result.put("responseTime", 100); // 模拟响应时间
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "连接测试失败：" + e.getMessage());
            log.error("AI模型连接测试失败: {}", model.getName(), e);
        }
        
        return result;
    }

    @Override
    public EmbeddingModel getEmbeddingOpenApi(String id) {
        EmbeddingModel embeddingModel = MAP_EMBEDDING_MODELS.get(id);
        if (embeddingModel == null) {
            insUpdDelOpenAiEmbeddingModel(id);
        }
        return MAP_EMBEDDING_MODELS.get(id);
    }

    /**
     * 脱敏API密钥
     *
     * @param modelVO 模型VO
     */
    private void maskApiKey(AiModelVO modelVO) {
        if (modelVO != null && StringUtils.hasText(modelVO.getApiKey())) {
            String apiKey = modelVO.getApiKey();
            if (apiKey.length() > 8) {
                modelVO.setApiKey(apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4));
            } else {
                modelVO.setApiKey("****");
            }
        }
    }

    /**
     * 插入、更新、删除OpenAiEmbeddingModel
     * @param id
     */
    private void insUpdDelOpenAiEmbeddingModel(String id){
        AiModel aiModel = baseMapper.selectById(id);
        if( ObjUtil.isNull(aiModel) || !"1".equals(aiModel.getStatus()) || (0!=aiModel.getDeleted()) ){
            MAP_EMBEDDING_MODELS.remove(id);
            log.debug("删除OpenAiEmbeddingModel: {}", id);
            return;
        }
        String modelId = aiModel.getModelId();
        String baseUrl = aiModel.getApiEndpoint();
        String apiKey = aiModel.getApiKey();
        if( ModelTypeEnum.Embeddings.getCode().equals(aiModel.getType()) ){
            MAP_EMBEDDING_MODELS.remove(id);
            OpenAiEmbeddingModel model = buildOpenAiEmbeddingModel(baseUrl, modelId, apiKey);
            MAP_EMBEDDING_MODELS.put(id, model);
        }
    }

    /**
     * 构建OpenAiEmbeddingModel
     * @param baseUrl
     * @param modelId
     * @param apiKey
     * @return
     */
    private OpenAiEmbeddingModel buildOpenAiEmbeddingModel(String baseUrl, String modelId, String apiKey) {
        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(baseUrl)
                .apiKey(StrUtil.isBlank(apiKey)?"not-used":apiKey) // 本地/私有部署一般无需鉴权
                .build();
        OpenAiEmbeddingOptions opts = new OpenAiEmbeddingOptions();
        opts.setModel(modelId);
        return new OpenAiEmbeddingModel(openAiApi, MetadataMode.EMBED, opts);
    }
}
