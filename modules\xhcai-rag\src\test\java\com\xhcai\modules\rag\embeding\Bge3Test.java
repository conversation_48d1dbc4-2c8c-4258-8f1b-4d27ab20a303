package com.xhcai.modules.rag.embeding;

import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;

import java.util.List;

public class Bge3Test {
    public static void main(String[] args) {

        String BASE_URL = "http://192.168.50.186:9998";
        String MODEL_ID = "bge-m3";

        /**
         * 定义 BGE-M3 专用 EmbeddingModel Bean。
         */
        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(BASE_URL)
                .apiKey("not-used") // 本地/私有部署一般无需鉴权
                .build();
        OpenAiEmbeddingOptions opts = new OpenAiEmbeddingOptions();
        opts.setModel(MODEL_ID);
        OpenAiEmbeddingModel openAiEmbeddingModel = new OpenAiEmbeddingModel(openAiApi, MetadataMode.EMBED, opts);
        float[] embed = openAiEmbeddingModel.embed("hello world，你好世界");
        for (float v : embed) {
            System.out.println(v);
        }

//        EmbeddingResponse embeddingResponse = openAiEmbeddingModel.embedForResponse(List.of("hello world，你好世界"));
//        System.out.println(embeddingResponse.getResults().get(0).getOutput());
//        System.out.println(embed.length);
    }
}
