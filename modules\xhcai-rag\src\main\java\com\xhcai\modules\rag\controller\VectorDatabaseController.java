package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.VectorDatabaseCreateDTO;
import com.xhcai.modules.rag.dto.VectorDatabaseQueryDTO;
import com.xhcai.modules.rag.dto.VectorDatabaseUpdateDTO;
import com.xhcai.modules.rag.service.IVectorDatabaseService;
import com.xhcai.modules.rag.vo.VectorDatabaseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 向量数据库管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/vector-database")
@Tag(name = "向量数据库管理", description = "向量数据库配置管理接口")
public class VectorDatabaseController {

    @Autowired
    private IVectorDatabaseService vectorDatabaseService;

    @GetMapping("/page")
    @Operation(summary = "分页查询向量数据库列表", description = "分页查询向量数据库配置列表")
    @RequiresPermissions("rag:vector-database:list")
    public Result<PageResult<VectorDatabaseVO>> getVectorDatabasePage(@Valid VectorDatabaseQueryDTO queryDTO) {
        PageResult<VectorDatabaseVO> result = vectorDatabaseService.selectVectorDatabasePage(queryDTO);
        return Result.success(result);
    }

    @GetMapping("/list")
    @Operation(summary = "查询向量数据库列表", description = "查询向量数据库配置列表")
    @RequiresPermissions("rag:vector-database:list")
    public Result<List<VectorDatabaseVO>> getVectorDatabaseList(@Valid VectorDatabaseQueryDTO queryDTO) {
        List<VectorDatabaseVO> result = vectorDatabaseService.selectVectorDatabaseList(queryDTO);
        return Result.success(result);
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询启用的向量数据库列表", description = "查询状态为启用的向量数据库列表")
    public Result<List<VectorDatabaseVO>> getEnabledVectorDatabases() {
        List<VectorDatabaseVO> result = vectorDatabaseService.getEnabledVectorDatabases();
        return Result.success(result);
    }

    @GetMapping("/default")
    @Operation(summary = "查询默认向量数据库", description = "查询当前设置为默认的向量数据库")
    public Result<VectorDatabaseVO> getDefaultVectorDatabase() {
        VectorDatabaseVO result = vectorDatabaseService.getDefaultVectorDatabase();
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查询向量数据库详情", description = "根据ID查询向量数据库详细信息")
    @RequiresPermissions("rag:vector-database:query")
    public Result<VectorDatabaseVO> getVectorDatabaseById(
            @Parameter(description = "向量数据库ID", required = true) @PathVariable String id) {
        VectorDatabaseVO result = vectorDatabaseService.selectVectorDatabaseById(id);
        return Result.success(result);
    }

    @PostMapping
    @Operation(summary = "创建向量数据库", description = "创建新的向量数据库配置")
    @RequiresPermissions("rag:vector-database:add")
    public Result<Boolean> createVectorDatabase(@Valid @RequestBody VectorDatabaseCreateDTO createDTO) {
        boolean result = vectorDatabaseService.insertVectorDatabase(createDTO);
        return Result.success(result);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新向量数据库", description = "更新向量数据库配置信息")
    @RequiresPermissions("rag:vector-database:edit")
    public Result<Boolean> updateVectorDatabase(
            @Parameter(description = "向量数据库ID", required = true) @PathVariable String id,
            @Valid @RequestBody VectorDatabaseUpdateDTO updateDTO) {
        boolean result = vectorDatabaseService.updateVectorDatabase(id, updateDTO);
        return Result.success(result);
    }

    @DeleteMapping("/{ids}")
    @Operation(summary = "删除向量数据库", description = "删除向量数据库配置（支持批量删除）")
    @RequiresPermissions("rag:vector-database:remove")
    public Result<Boolean> deleteVectorDatabases(
            @Parameter(description = "向量数据库ID列表，多个ID用逗号分隔", required = true) @PathVariable String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        boolean result = vectorDatabaseService.deleteVectorDatabases(idList);
        return Result.success(result);
    }

    @PutMapping("/default/{id}")
    @Operation(summary = "设置默认向量数据库", description = "设置指定的向量数据库为默认数据库")
    @RequiresPermissions("rag:vector-database:edit")
    public Result<Boolean> setDefaultVectorDatabase(
            @Parameter(description = "向量数据库ID", required = true) @PathVariable String id) {
        boolean result = vectorDatabaseService.setDefaultVectorDatabase(id);
        return Result.success(result);
    }

    @PostMapping("/{id}/test")
    @Operation(summary = "测试向量数据库连接", description = "测试指定向量数据库的连接状态")
    @RequiresPermissions("rag:vector-database:test")
    public Result<Map<String, Object>> testConnection(
            @Parameter(description = "向量数据库ID", required = true) @PathVariable String id) {
        Map<String, Object> result = vectorDatabaseService.testConnection(id);
        return Result.success(result);
    }

    @PostMapping("/test")
    @Operation(summary = "测试向量数据库连接", description = "使用配置信息测试向量数据库连接")
    @RequiresPermissions("rag:vector-database:test")
    public Result<Map<String, Object>> testConnection(@Valid @RequestBody VectorDatabaseCreateDTO createDTO) {
        Map<String, Object> result = vectorDatabaseService.testConnection(createDTO);
        return Result.success(result);
    }

    @GetMapping("/{id}/statistics")
    @Operation(summary = "查询向量数据库统计信息", description = "查询向量数据库的统计信息")
    @RequiresPermissions("rag:vector-database:query")
    public Result<Map<String, Object>> getStatistics(
            @Parameter(description = "向量数据库ID", required = true) @PathVariable String id) {
        Map<String, Object> result = vectorDatabaseService.getStatistics(id);
        return Result.success(result);
    }

    @PostMapping("/{id}/initialize")
    @Operation(summary = "初始化向量数据库", description = "初始化向量数据库结构和配置")
    @RequiresPermissions("rag:vector-database:manage")
    public Result<Boolean> initializeVectorDatabase(
            @Parameter(description = "向量数据库ID", required = true) @PathVariable String id) {
        boolean result = vectorDatabaseService.initializeVectorDatabase(id);
        return Result.success(result);
    }

    @GetMapping("/export")
    @Operation(summary = "导出向量数据库配置", description = "导出向量数据库配置信息")
    @RequiresPermissions("rag:vector-database:export")
    public Result<List<VectorDatabaseVO>> exportVectorDatabases(@Valid VectorDatabaseQueryDTO queryDTO) {
        List<VectorDatabaseVO> result = vectorDatabaseService.exportVectorDatabases(queryDTO);
        return Result.success(result);
    }

    @GetMapping("/exists")
    @Operation(summary = "检查向量数据库名称是否存在", description = "检查指定名称的向量数据库是否已存在")
    @RequiresPermissions("rag:vector-database:query")
    public Result<Boolean> existsVectorDatabaseName(
            @Parameter(description = "向量数据库名称", required = true) @RequestParam String name,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String excludeId) {
        boolean result = vectorDatabaseService.existsVectorDatabaseName(name, excludeId);
        return Result.success(result);
    }
}
