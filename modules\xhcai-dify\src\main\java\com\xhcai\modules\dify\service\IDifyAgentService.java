package com.xhcai.modules.dify.service;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.dto.agent.DifyAgentDTO;
import com.xhcai.modules.dify.dto.agent.DifyChatRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyChatResponseDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCloneRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyAppsListResponseDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 智能体服务接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IDifyAgentService {


    /**
     * 创建指定平台的第三方智能体（调用Dify Console API）
     *
     * @param createRequest 创建请求
     * @return 创建结果
     */
    Mono<Result<DifyAgentCreateResponseDTO>> createThirdPartyAgent(DifyAgentCreateRequestDTO createRequest);

    /**
     * 获取指定平台的智能体列表
     *
     * @param platformId 平台ID
     * @param page 页码，默认1
     * @param limit 每页数量，默认30
     * @param name 智能体名称过滤（可选）
     * @param isCreatedByMe 是否只获取我创建的智能体，默认true
     * @return 智能体列表
     */
    Mono<Result<DifyAppsListResponseDTO>> getAgentsList(String platformId, Integer page, Integer limit, String name, Boolean isCreatedByMe);

    /**
     * 克隆指定平台的第三方智能体（调用Dify Console API）
     *
     * @param cloneRequest 克隆请求
     * @return 克隆结果
     */
    Mono<Result<DifyAgentCreateResponseDTO>> cloneThirdPartyAgent(DifyAgentCloneRequestDTO cloneRequest);
}
