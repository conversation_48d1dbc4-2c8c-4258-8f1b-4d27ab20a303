package com.xhcai.modules.rag.entity;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import com.xhcai.common.datasource.handler.PostgreSQLJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/**
 * 文档分段实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "文档分段实体")
@Entity
@Table(name = "document_segments")
@TableName(value = "document_segments", autoResultMap = true)
public class DocumentSegment extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID", example = "dataset123")
    @Column(name = "dataset_id", nullable = false, length = 36)
    @TableField("dataset_id")
    private String datasetId;

    /**
     * 文档ID
     */
    @Schema(description = "文档ID", example = "doc123")
    @Column(name = "document_id", nullable = false, length = 36)
    @TableField("document_id")
    private String documentId;

    /**
     * 文档分段排序号
     */
    @Schema(description = "文档分段排序号", example = "1")
    @Column(name = "position", nullable = false)
    @TableField("position")
    private Integer position;

    /**
     * 内容
     */
    @Schema(description = "分段内容", example = "这是一个文档分段的内容...")
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    @TableField("content")
    private String content;

    /**
     * 字符数
     */
    @Schema(description = "字符数", example = "150")
    @Column(name = "word_count", nullable = false)
    @TableField("word_count")
    private Integer wordCount;

    /**
     * token数
     */
    @Schema(description = "token数", example = "50")
    @Column(name = "tokens", nullable = false)
    @TableField("tokens")
    private Integer tokens;

    /**
     * 段落关键字
     */
    @Schema(description = "段落关键字列表", example = "[\"关键字1\", \"关键字2\"]")
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "keywords", columnDefinition = "json")
    @TableField(value = "keywords", typeHandler = PostgreSQLJsonTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private List<String> keywords;

    /**
     * 索引节点ID
     */
    @Schema(description = "索引节点ID", example = "node123")
    @Column(name = "index_node_id", length = 255)
    @TableField("index_node_id")
    private String indexNodeId;

    /**
     * 索引节点哈希
     */
    @Schema(description = "索引节点哈希", example = "abc123def456")
    @Column(name = "index_node_hash", length = 255)
    @TableField("index_node_hash")
    private String indexNodeHash;

    /**
     * 命中次数
     */
    @Schema(description = "命中次数", example = "10")
    @Column(name = "hit_count", nullable = false)
    @TableField("hit_count")
    private Integer hitCount = 0;

    /**
     * 是否启用 1:启用 0:停用
     */
    @Schema(description = "是否启用", example = "true")
    @Column(name = "enabled", nullable = false)
    @TableField("enabled")
    private Boolean enabled = true;

    /**
     * 启停时间
     */
    @Schema(description = "启停时间", example = "2024-01-01 12:00:00")
    @Column(name = "disabled_at")
    @TableField("disabled_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime disabledAt;

    /**
     * 启停异动人ID
     */
    @Column(name = "disabled_by", length = 36)
    @TableField("disabled_by")
    private String disabledBy;

    /**
     * 向量化状态 completed:完成 error:异常 indexing:索引中
     */
    @Column(name = "status", nullable = false, length = 255)
    @TableField("status")
    private String status = "waiting";

    /**
     * 索引时间
     */
    @Column(name = "indexing_at")
    @TableField("indexing_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime indexingAt;

    /**
     * 完成时间
     */
    @Column(name = "completed_at")
    @TableField("completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt;

    /**
     * 异常原因
     */
    @Column(name = "error", columnDefinition = "TEXT")
    @TableField("error")
    private String error;

    /**
     * 异常停止时间
     */
    @Column(name = "stopped_at")
    @TableField("stopped_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime stoppedAt;

    /**
     * 答案内容
     */
    @Column(name = "answer", columnDefinition = "TEXT")
    @TableField("answer")
    private String answer;

    @Override
    public String toString() {
        return "DocumentSegment{"
                + "id='" + getId() + '\''
                + ", datasetId='" + datasetId + '\''
                + ", documentId='" + documentId + '\''
                + ", position=" + position
                + ", wordCount=" + wordCount
                + ", tokens=" + tokens
                + ", enabled=" + enabled
                + ", status='" + status + '\''
                + ", tenantId='" + getTenantId() + '\''
                + ", createBy='" + getCreateBy() + '\''
                + ", createTime=" + getCreateTime()
                + ", updatedTime=" + getUpdateTime()
                + '}';
    }
}
