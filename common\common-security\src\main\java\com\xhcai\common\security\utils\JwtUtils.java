package com.xhcai.common.security.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.Map;

/**
 * JWT工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtUtils {

    private static final Logger log = LoggerFactory.getLogger(JwtUtils.class);

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:china-shenzhengshi-xinghuo-dz-company-ai-plus-plat-secret-key-20250916}")
    private String secret;

    /**
     * JWT过期时间（秒）
     */
    @Value("${jwt.expiration:86400}")
    private Long expiration;

    /**
     * JWT刷新时间（秒）
     */
    @Value("${jwt.refresh-expiration:604800}")
    private Long refreshExpiration;

    /**
     * JWT签发者
     */
    @Value("${jwt.issuer:xhcai-plus}")
    private String issuer;

    /**
     * 生成JWT Token
     *
     * @param subject 主题（通常是用户ID）
     * @param claims  自定义声明
     * @return JWT Token
     */
    public String generateToken(String subject, Map<String, Object> claims) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        JwtBuilder builder = Jwts.builder()
                .subject(subject)
                .issuer(issuer)
                .issuedAt(now)
                .expiration(expiryDate)
                .signWith(getSigningKey(), Jwts.SIG.HS512);

        if (claims != null && !claims.isEmpty()) {
            builder.claims(claims);
        }

        return builder.compact();
    }

    /**
     * 生成JWT Token（无自定义声明）
     *
     * @param subject 主题（通常是用户ID）
     * @return JWT Token
     */
    public String generateToken(String subject) {
        return generateToken(subject, null);
    }

    /**
     * 生成刷新Token
     *
     * @param subject 主题（通常是用户ID）
     * @return 刷新Token
     */
    public String generateRefreshToken(String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshExpiration * 1000);

        return Jwts.builder()
                .subject(subject)
                .issuer(issuer)
                .issuedAt(now)
                .expiration(expiryDate)
                .claim("type", "refresh")
                .signWith(getSigningKey(), Jwts.SIG.HS512)
                .compact();
    }

    /**
     * 从Token中获取主题
     *
     * @param token JWT Token
     * @return 主题
     */
    public String getSubjectFromToken(String token) {
        try {
            Claims claims = getClaimsFromTokenOrApiKey(token);
            return claims.getSubject();
        } catch (Exception e) {
            log.error("获取Token主题失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从Token中获取声明
     *
     * @param token JWT Token
     * @return 声明
     */
    public Claims getClaimsFromTokenOrApiKey(String token) {
        return Jwts.parser()
                .verifyWith(getSigningKey())
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    /**
     * 从Token中获取过期时间
     *
     * @param token JWT Token
     * @return 过期时间
     */
    public Date getExpirationDateFromTokenOrApiKey(String token) {
        try {
            Claims claims = getClaimsFromTokenOrApiKey(token);
            return claims.getExpiration();
        } catch (Exception e) {
            log.error("获取Token过期时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证Token是否有效
     *
     * @param token JWT Token
     * @return 是否有效
     */
    public boolean validateTokenOrApiKey(String token) {
        try {
            Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token);
            return true;
        } catch (SecurityException e) {
            log.error("JWT签名无效: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("JWT格式错误: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            log.error("JWT已过期: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("不支持的JWT: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("JWT参数为空: {}", e.getMessage());
        } catch (Exception e) {
            log.error("JWT验证失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 检查Token是否过期
     *
     * @param token JWT Token
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromTokenOrApiKey(token);
            return expiration != null && expiration.before(new Date());
        } catch (Exception e) {
            log.error("检查Token过期状态失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 检查Token是否即将过期（30分钟内）
     *
     * @param token JWT Token
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String token) {
        try {
            Date expiration = getExpirationDateFromTokenOrApiKey(token);
            if (expiration == null) {
                return true;
            }
            long timeUntilExpiration = expiration.getTime() - System.currentTimeMillis();
            return timeUntilExpiration < 30 * 60 * 1000; // 30分钟
        } catch (Exception e) {
            log.error("检查Token即将过期状态失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 刷新Token
     *
     * @param token 原Token
     * @return 新Token
     */
    public String refreshToken(String token) {
        try {
            Claims claims = getClaimsFromTokenOrApiKey(token);
            String subject = claims.getSubject();
            
            // 移除时间相关的声明
            claims.remove("iat");
            claims.remove("exp");
            
            return generateToken(subject, claims);
        } catch (Exception e) {
            log.error("刷新Token失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    public SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 从Token中获取用户ID
     *
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            String subject = getSubjectFromToken(token);
            return subject != null ? Long.parseLong(subject) : null;
        } catch (NumberFormatException e) {
            log.error("Token中的用户ID格式错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从Token中获取用户名
     *
     * @param token JWT Token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            Claims claims = getClaimsFromTokenOrApiKey(token);
            return claims.get("username", String.class);
        } catch (Exception e) {
            log.error("获取Token中的用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从Token中获取租户ID
     *
     * @param token JWT Token
     * @return 租户ID
     */
    public Long getTenantIdFromToken(String token) {
        try {
            Claims claims = getClaimsFromTokenOrApiKey(token);
            Object tenantId = claims.get("tenantId");
            return tenantId != null ? Long.parseLong(tenantId.toString()) : null;
        } catch (Exception e) {
            log.error("获取Token中的租户ID失败: {}", e.getMessage());
            return null;
        }
    }
}
