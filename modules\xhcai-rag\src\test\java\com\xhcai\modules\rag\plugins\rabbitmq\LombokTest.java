package com.xhcai.modules.rag.plugins.rabbitmq;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * Lombok 测试 - 验证 @Slf4j 注解是否正常工作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class LombokTest {
    
    @Test
    public void testSlf4jAnnotation() {
        log.info("Lombok @Slf4j annotation is working in RabbitMQ module!");
        log.debug("Debug message test");
        log.warn("Warning message test");
        log.error("Error message test");
    }
    
    @Test
    public void testLogLevels() {
        // 测试不同级别的日志
        log.trace("This is a trace message");
        log.debug("This is a debug message");
        log.info("This is an info message");
        log.warn("This is a warning message");
        log.error("This is an error message");
    }
}
