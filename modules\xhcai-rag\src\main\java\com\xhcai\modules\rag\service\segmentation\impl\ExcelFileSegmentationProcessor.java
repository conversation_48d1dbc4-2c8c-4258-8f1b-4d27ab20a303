package com.xhcai.modules.rag.service.segmentation.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhcai.modules.rag.common.FileCommon;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import com.xhcai.modules.rag.service.segmentation.AbstractFileSegmentationProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * Excel文件分段处理器
 * 1：每一行即为一段。默认第一行是表头，如果第一行不存在数据，则取第一次有数据的行
 * 2：除了表头行外
 *    2.1 如果行的列数与表头行的列数一样，内容读取为 表头 : 内容
 *    2.1 如果行的列数与表头行的列数不一样，内容读取为 当前行列的内容
 * 3: 按 natural 分段, 会先按段落分段，然后将多个段落合并为一个自然段
 * 4: 非natural分段，按段落分段，然后5个段落合并为自然段
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class ExcelFileSegmentationProcessor extends AbstractFileSegmentationProcessor {
    private final static int DEFAULT_SEGMENTS = 5;

    @Override
    public List<String> getSupportedFileTypes() {
        return Arrays.asList("xls", "xlsx");
    }

    @Override
    public String getProcessorName() {
        return "Excel文件分段处理器";
    }

    @Override
    public int getPriority() {
        return 40;
    }

    @Override
    public List<SegmentResult> processSegmentation(Document document, InputStream inputStream) throws Exception {
        log.info("开始处理Excel文件分段: documentId={}, fileName={}", document.getId(), document.getName());

        try {
            SegmentConfig segmentConfig = document.getSegmentConfig();
            CleaningConfig cleaningConfig = document.getCleaningConfig();
            String type = segmentConfig.getType();
            List<SegmentResult> segments = extractSegmentsFromExcel(document, inputStream);

            if("natural".equals(type)){
                segments = segmentByParagraphs(segments, document.getSegmentConfig().getNatural());
            }else {
                SegmentConfig.NaturalConfig natural = new SegmentConfig.NaturalConfig();
                natural.setSegments(DEFAULT_SEGMENTS);
                segments = segmentByParagraphs(segments, natural);
            }

            log.info("Excel文件分段完成: documentId={}, 分段数量={}", document.getId(), segments.size());
            return segments;
        } catch (Exception e) {
            log.error("Excel文件分段处理失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            throw new Exception("Excel文件分段处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按段落分段文本
     *
     * @param segments   原始的分段的数据
     * @param mapNatural 分段配置参数
     *     {
     *         "segments": "3"
     *     }
     * @return 分段结果列表
     */
    protected List<SegmentResult> segmentByParagraphs(List<SegmentResult> segments, SegmentConfig.NaturalConfig mapNatural) {
        // 每 segmentsNum 个段落合并成一个自然段
        int segmentsNum = mapNatural.getSegments();
        for (int i = 0; i < segments.size(); i += segmentsNum) {
            int end = Math.min(i + segmentsNum, segments.size());
            List<SegmentResult> segmentResults = segments.subList(i, end);
            List<String> contents = segmentResults.stream().map(SegmentResult::getContent).toList();
            String joinContent = CollUtil.join(contents, FileCommon.LINE_SEPARATOR);
            segments.add(SegmentResult.create(joinContent, Math.ceilDiv(i + 1, segmentsNum), extractKeywords(joinContent)));
        }
        return segments;
    }

    /**
     * 从Excel文件提取分段
     * 每一行即为一段
     */
    public List<SegmentResult> extractSegmentsFromExcel(Document document, InputStream inputStream) throws Exception {
        String fileExtension = document.getDocType();
        Workbook workbook = null;
        List<SegmentResult> segments = new java.util.ArrayList<>();

        try {
            if ("xlsx".equals(fileExtension)) {
                workbook = new XSSFWorkbook(inputStream);
            } else if ("xls".equals(fileExtension)) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new Exception("不支持的Excel文件格式: " + fileExtension);
            }

            DataFormatter formatter = new DataFormatter();
            int segmentPosition = 1;

            // 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();

                // 查找表头行
                List<String> headers = findHeaderRow(sheet, formatter);
                if (headers.isEmpty()) {
                    log.warn("工作表 {} 没有找到有效的表头行，跳过处理", sheetName);
                    continue;
                }

                // 处理数据行
                int headerRowIndex = findHeaderRowIndex(sheet, formatter);
                int processedRows = 0;

                for (Row row : sheet) {
                    // 跳过表头行
                    if (row.getRowNum() <= headerRowIndex) {
                        continue;
                    }

                    List<String> rowData = extractRowData(row, formatter);
                    if (rowData.isEmpty()) {
                        continue; // 跳过空行
                    }

                    String rowContent = formatRowContent(headers, rowData);
                    if (!rowContent.trim().isEmpty()) {
                        // 提取关键词
                        List<String> keywords = extractKeywords(rowContent);

                        // 创建分段
                        SegmentResult segment = SegmentResult.create(rowContent, segmentPosition++, keywords);
                        segments.add(segment);

                        log.debug("创建Excel分段: position={}, content={}",
                                segment.getPosition(),
                                rowContent.length() > 100 ? rowContent.substring(0, 100) + "..." : rowContent);

                        processedRows++;
                    }
                }

                log.debug("工作表 {} 处理完成，处理了 {} 行数据", sheetName, processedRows);
            }

            return segments;
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.warn("关闭Excel工作簿失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 从Excel文件提取文本
     * 每一行即为一段，按照表头-内容格式进行分段
     *
     * @param document    文档信息
     * @param inputStream 输入流
     * @return 提取的文本，每行为一个分段
     * @throws Exception 提取异常
     */
    public String extractTextFromExcel(Document document, InputStream inputStream) throws Exception {
        String fileExtension = document.getDocType();
        Workbook workbook = null;

        try {
            if ("xlsx".equals(fileExtension)) {
                workbook = new XSSFWorkbook(inputStream);
            } else if ("xls".equals(fileExtension)) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new Exception("不支持的Excel文件格式: " + fileExtension);
            }

            StringBuilder content = new StringBuilder();
            DataFormatter formatter = new DataFormatter();

            // 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();

                // 查找表头行
                List<String> headers = findHeaderRow(sheet, formatter);
                if (headers.isEmpty()) {
                    log.warn("工作表 {} 没有找到有效的表头行，跳过处理", sheetName);
                    continue;
                }

                // 处理数据行
                int headerRowIndex = findHeaderRowIndex(sheet, formatter);
                int processedRows = 0;

                for (Row row : sheet) {
                    // 跳过表头行
                    if (row.getRowNum() <= headerRowIndex) {
                        continue;
                    }

                    List<String> rowData = extractRowData(row, formatter);
                    if (rowData.isEmpty()) {
                        continue; // 跳过空行
                    }

                    String rowContent = formatRowContent(headers, rowData);
                    if (!rowContent.trim().isEmpty()) {
                        content.append(rowContent).append(StrUtil.LF);
                        processedRows++;
                    }
                }
            }

            return content.toString();
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.warn("关闭Excel工作簿失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 查找表头行
     * 默认第一行是表头，如果第一行不存在数据，则取第一次有数据的行
     */
    private List<String> findHeaderRow(Sheet sheet, DataFormatter formatter) {
        for (Row row : sheet) {
            List<String> rowData = extractRowData(row, formatter);
            if (!rowData.isEmpty()) {
                log.debug("找到表头行，行号: {}, 数据: {}", row.getRowNum(), rowData);
                return rowData;
            }
        }
        return List.of(); // 返回空列表
    }

    /**
     * 查找表头行的索引
     */
    private int findHeaderRowIndex(Sheet sheet, DataFormatter formatter) {
        for (Row row : sheet) {
            List<String> rowData = extractRowData(row, formatter);
            if (!rowData.isEmpty()) {
                return row.getRowNum();
            }
        }
        return -1;
    }

    /**
     * 提取行数据
     */
    private List<String> extractRowData(Row row, DataFormatter formatter) {
        List<String> rowData = new java.util.ArrayList<>();

        if (row == null) {
            return rowData;
        }

        // 获取行中最后一个有数据的单元格索引
        int lastCellNum = row.getLastCellNum();
        if (lastCellNum <= 0) {
            return rowData;
        }

        for (int cellIndex = 0; cellIndex < lastCellNum; cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            String cellValue = "";

            if (cell != null) {
                cellValue = formatter.formatCellValue(cell);
            }

            rowData.add(cellValue != null ? cellValue.trim() : "");
        }

        // 移除末尾的空字符串
        while (!rowData.isEmpty() && rowData.getLast().isEmpty()) {
            rowData.removeLast();
        }

        return rowData;
    }

    /**
     * 格式化行内容
     * 如果行的列数与表头行的列数一样，内容读取为 表头 : 内容
     * 如果行的列数与表头行的列数不一样，内容读取为 当前行列的内容
     */
    private String formatRowContent(List<String> headers, List<String> rowData) {
        StringBuilder content = new StringBuilder();

        if (rowData.size() == headers.size()) {
            // 列数相同，使用 表头:内容 格式
            for (int i = 0; i < headers.size(); i++) {
                String header = headers.get(i);
                String data = rowData.get(i);

                if (!data.isEmpty()) {
                    if (!content.isEmpty()) {
                        content.append(", ");
                    }
                    content.append(header).append(": ").append(data);
                }
            }
        } else {
            // 列数不同，直接使用当前行的内容
            for (String data : rowData) {
                if (!data.isEmpty()) {
                    if (!content.isEmpty()) {
                        content.append(", ");
                    }
                    content.append(data);
                }
            }
        }

        return content.toString();
    }
}
