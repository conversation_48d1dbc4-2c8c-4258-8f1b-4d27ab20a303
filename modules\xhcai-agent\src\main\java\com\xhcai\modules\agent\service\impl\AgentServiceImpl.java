package com.xhcai.modules.agent.service.impl;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.dto.agent.DifyAgentCloneRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateResponseDTO;
import com.xhcai.modules.dify.entity.ThirdPlatform;
import com.xhcai.modules.dify.mapper.ThirdPlatformMapper;
import com.xhcai.modules.dify.service.IDifyAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.utils.JsonUtils;
import com.xhcai.common.datasource.plugin.TenantContextHolder;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.agent.dto.AgentCreateDTO;
import com.xhcai.modules.agent.dto.AgentQueryDTO;
import com.xhcai.modules.agent.dto.AgentUpdateDTO;
import com.xhcai.modules.agent.dto.ThirdPartyAgentCreateDTO;
import com.xhcai.modules.agent.dto.ThirdPartyAgentCloneDTO;
import com.xhcai.modules.agent.entity.Agent;
import com.xhcai.modules.agent.mapper.AgentMapper;

import com.xhcai.modules.agent.mapper.AgentMapper.AgentStatsVO;
import com.xhcai.modules.agent.service.IAgentService;
import com.xhcai.modules.agent.vo.AgentVO;
import reactor.core.publisher.Mono;

/**
 * 智能体服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class AgentServiceImpl extends ServiceImpl<AgentMapper, Agent> implements IAgentService {

    private static final Logger log = LoggerFactory.getLogger(AgentServiceImpl.class);

    @Autowired
    private ThirdPlatformMapper thirdPlatformMapper;

    @Autowired
    IDifyAgentService iDifyAgentService;



    @Override
    public PageResult<AgentVO> getAgentPage(AgentQueryDTO queryDTO) {
        Page<AgentVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

        // 检查是否是平台管理员
        boolean isPlatformAdmin = SecurityUtils.isPlatformAdmin();

        IPage<AgentVO> result;
        if (isPlatformAdmin) {
            // 平台管理员可以查看所有租户的智能体，需要清除租户上下文
            result = TenantContextHolder.callWithTenant(null, ()
                    -> baseMapper.selectAgentPageForPlatformAdmin(page, queryDTO)
            );
        } else {
            // 普通用户只能查看自己租户的智能体
            result = baseMapper.selectAgentPage(page, queryDTO);
        }

        // 优化外部智能体信息填充，使用批量查询 + 并发处理
        List<AgentVO> records = result.getRecords();
        fillExternalAgentDetailsBatch(records);

        // 设置appId字段（将external_agent_id映射为appId）
        records.forEach(agent -> {
            if (agent.getExternalAgentId() != null) {
                agent.setAppId(agent.getExternalAgentId());
            }
        });

        return PageResult.of(records, result.getTotal(), page.getCurrent(), page.getSize());
    }

    @Override
    public AgentVO getAgentById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        AgentVO agentVO = baseMapper.selectAgentById(id);
        if (agentVO == null) {
            throw new BusinessException("智能体不存在");
        }

        return agentVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAgent(AgentCreateDTO createDTO) {
        // 检查名称是否存在
        if (checkNameExists(createDTO.getName(), null)) {
            throw new BusinessException("智能体名称已存在");
        }

        // 创建智能体实体
        Agent agent = new Agent();
        BeanUtils.copyProperties(createDTO, agent);

        // 设置默认值
        agent.setStatus("1"); // 默认启用
        agent.setIsPublic(createDTO.getIsPublic() != null ? createDTO.getIsPublic() : false);
        agent.setSortOrder(createDTO.getSortOrder() != null ? createDTO.getSortOrder() : 0);
        agent.setVersion(StringUtils.hasText(createDTO.getVersion()) ? createDTO.getVersion() : "1.0.0");
        agent.setConversationCount(0L);

        // 设置智能体来源类型，默认为本平台
        agent.setSourceType(StringUtils.hasText(createDTO.getSourceType()) ? createDTO.getSourceType() : "platform");

        // 处理外部智能体相关信息
        if ("external".equals(agent.getSourceType())) {
            // 验证外部智能体必填字段
            if (!StringUtils.hasText(createDTO.getExternalAgentId())) {
                throw new BusinessException("外部智能体ID不能为空");
            }

            // 设置外部智能体信息
            agent.setExternalAgentId(createDTO.getExternalAgentId());
        }

        // 显式设置租户ID（确保平台管理员创建的智能体也有正确的租户归属）
        String currentTenantId = SecurityUtils.getCurrentTenantId();
        if (currentTenantId != null) {
            agent.setTenantId(currentTenantId);
            log.debug("设置智能体租户ID: {}", currentTenantId);
        } else {
            log.warn("当前用户没有租户ID，智能体将由MyMetaObjectHandler自动填充租户信息");
        }

        // 验证配置
        if (!validateAgentConfig(agent)) {
            throw new BusinessException("智能体配置验证失败");
        }

        // 保存到数据库
        if (!save(agent)) {
            throw new BusinessException("创建智能体失败");
        }

        log.info("创建智能体成功，ID: {}, 名称: {}, 来源类型: {}", agent.getId(), agent.getName(), agent.getSourceType());
        return agent.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAgent(AgentUpdateDTO updateDTO) {
        // 检查智能体是否存在
        Agent existingAgent = getById(updateDTO.getId());
        if (existingAgent == null) {
            throw new BusinessException("智能体不存在");
        }

        // 检查名称是否重复
        if (StringUtils.hasText(updateDTO.getName())
                && checkNameExists(updateDTO.getName(), updateDTO.getId())) {
            throw new BusinessException("智能体名称已存在");
        }

        // 更新实体
        Agent agent = new Agent();
        BeanUtils.copyProperties(updateDTO, agent);

        // 验证配置
        if (!validateAgentConfig(agent)) {
            throw new BusinessException("智能体配置验证失败");
        }

        boolean result = updateById(agent);
        if (result) {
            log.info("更新智能体成功，ID: {}", updateDTO.getId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAgent(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        Agent agent = getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        boolean result = removeById(id);
        if (result) {
            log.info("删除智能体成功，ID: {}, 名称: {}", id, agent.getName());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteAgents(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("智能体ID列表不能为空");
        }

        boolean result = removeByIds(ids);
        if (result) {
            log.info("批量删除智能体成功，数量: {}", ids.size());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableAgent(String id) {
        return updateAgentStatus(id, "1");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableAgent(String id) {
        return updateAgentStatus(id, "0");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, String status) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("智能体ID列表不能为空");
        }

        if (!StringUtils.hasText(status) || (!status.equals("0") && !status.equals("1"))) {
            throw new BusinessException("状态值无效");
        }

        String currentUser = SecurityUtils.getCurrentUserId();
        int result = baseMapper.batchUpdateStatus(ids, status, currentUser);

        if (result > 0) {
            log.info("批量更新智能体状态成功，数量: {}, 状态: {}", result, status);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyAgent(String id, String name) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        if (!StringUtils.hasText(name)) {
            throw new BusinessException("新名称不能为空");
        }

        // 检查名称是否存在
        if (checkNameExists(name, null)) {
            throw new BusinessException("智能体名称已存在");
        }

        // 获取原智能体
        Agent originalAgent = getById(id);
        if (originalAgent == null) {
            throw new BusinessException("原智能体不存在");
        }

        // 创建副本
        Agent newAgent = new Agent();
        BeanUtils.copyProperties(originalAgent, newAgent);
        newAgent.setId(null); // 清空ID，让系统自动生成
        newAgent.setName(name);
        newAgent.setStatus("0"); // 默认禁用
        newAgent.setIsPublic(false); // 默认私有
        newAgent.setConversationCount(0L);
        newAgent.setPublishedAt(null);
        newAgent.setLastConversationAt(null);

        if (!save(newAgent)) {
            throw new BusinessException("复制智能体失败");
        }

        log.info("复制智能体成功，原ID: {}, 新ID: {}, 新名称: {}", id, newAgent.getId(), name);
        return newAgent.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishAgent(String id, String version) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        Agent agent = getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        agent.setVersion(StringUtils.hasText(version) ? version : "1.0.0");
        agent.setPublishedAt(LocalDateTime.now());
        agent.setStatus("1"); // 发布时自动启用

        boolean result = updateById(agent);
        if (result) {
            log.info("发布智能体成功，ID: {}, 版本: {}", id, agent.getVersion());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setAgentPublic(String id, boolean isPublic) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        Agent agent = getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        agent.setIsPublic(isPublic);
        boolean result = updateById(agent);

        if (result) {
            log.info("设置智能体公开状态成功，ID: {}, 公开: {}", id, isPublic);
        }

        return result;
    }

    @Override
    public List<AgentVO> getUserAgents(String userId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        return baseMapper.selectAgentsByUser(userId, tenantId);
    }

    @Override
    public List<AgentVO> getPublicAgents() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        return baseMapper.selectPublicAgents(tenantId);
    }

    @Override
    public boolean checkNameExists(String name, String excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        Long count = baseMapper.checkNameExists(name, tenantId, excludeId);
        return count != null && count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConversationStats(String id) {
        // 这里可以实现更新对话统计的逻辑
        // 通常会从对话表中统计数据然后更新到智能体表
        return true;
    }

    @Override
    public AgentStatsVO getAgentStats() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        return baseMapper.getAgentStats(tenantId);
    }

    @Override
    public List<AgentVO> getAssociatedAgents(String platformId) {
        LambdaQueryWrapper<Agent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Agent::getSourceType, "external")
               .isNotNull(Agent::getExternalAgentId);

        // 如果指定了平台ID，则只查询该平台的关联智能体
        if (StringUtils.hasText(platformId)) {
            wrapper.eq(Agent::getPlatformId, platformId);
        }

        List<Agent> agents = this.list(wrapper);

        // 转换为VO
        return agents.stream().map(agent -> {
            AgentVO vo = new AgentVO();
            vo.setId(agent.getId());
            vo.setName(agent.getName());
            vo.setExternalAgentId(agent.getExternalAgentId());
            vo.setPlatformId(agent.getPlatformId());
            vo.setSourceType(agent.getSourceType());
            return vo;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<AgentVO> getAiExploreAgents() {
        // 获取当前用户和租户信息
        String currentUserId = SecurityUtils.getCurrentUserId();
        String currentTenantId = SecurityUtils.getCurrentTenantId();

        // 使用Mapper中的SQL查询
        List<AgentVO> agents = baseMapper.selectAiExploreAgents(currentTenantId, currentUserId);

        // 设置appId字段映射（external_agent_id -> appId）
        agents.forEach(agent -> {
            agent.setAppId(agent.getExternalAgentId());
        });

        return agents;
    }

    @Override
    public boolean validateAgentConfig(Agent agent) {
        // 验证模型配置
        if (StringUtils.hasText(agent.getModelConfig())) {
            try {
                JsonUtils.parseObject(agent.getModelConfig(), Object.class);
            } catch (Exception e) {
                log.warn("模型配置JSON格式无效: {}", agent.getModelConfig());
                return false;
            }
        }

        // 验证工具配置
        if (StringUtils.hasText(agent.getToolsConfig())) {
            try {
                JsonUtils.parseObject(agent.getToolsConfig(), Object.class);
            } catch (Exception e) {
                log.warn("工具配置JSON格式无效: {}", agent.getToolsConfig());
                return false;
            }
        }

        // 验证知识库配置
        if (StringUtils.hasText(agent.getKnowledgeConfig())) {
            try {
                JsonUtils.parseObject(agent.getKnowledgeConfig(), Object.class);
            } catch (Exception e) {
                log.warn("知识库配置JSON格式无效: {}", agent.getKnowledgeConfig());
                return false;
            }
        }

        // 验证对话配置
        if (StringUtils.hasText(agent.getConversationConfig())) {
            try {
                JsonUtils.parseObject(agent.getConversationConfig(), Object.class);
            } catch (Exception e) {
                log.warn("对话配置JSON格式无效: {}", agent.getConversationConfig());
                return false;
            }
        }

        return true;
    }

    @Override
    public String exportAgentConfig(String id) {
        Agent agent = getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        // 导出配置（排除敏感信息）
        Agent exportAgent = new Agent();
        BeanUtils.copyProperties(agent, exportAgent);
        exportAgent.setId(null);
        exportAgent.setTenantId(null);
        exportAgent.setCreateBy(null);
        exportAgent.setUpdateBy(null);
        exportAgent.setCreateTime(null);
        exportAgent.setUpdateTime(null);
        exportAgent.setConversationCount(null);
        exportAgent.setLastConversationAt(null);

        return JsonUtils.toJsonString(exportAgent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importAgentConfig(String configJson, String name) {
        try {
            Agent agent = JsonUtils.parseObject(configJson, Agent.class);
            if (agent == null) {
                throw new BusinessException("配置格式无效");
            }

            agent.setName(name);
            agent.setStatus("0"); // 默认禁用
            agent.setIsPublic(false); // 默认私有
            agent.setConversationCount(0L);

            if (!validateAgentConfig(agent)) {
                throw new BusinessException("智能体配置验证失败");
            }

            if (!save(agent)) {
                throw new BusinessException("导入智能体失败");
            }

            log.info("导入智能体成功，ID: {}, 名称: {}", agent.getId(), name);
            return agent.getId();
        } catch (Exception e) {
            log.error("导入智能体配置失败", e);
            throw new BusinessException("导入智能体配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新智能体状态
     */
    private boolean updateAgentStatus(String id, String status) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        Agent agent = getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        agent.setStatus(status);
        boolean result = updateById(agent);

        if (result) {
            log.info("更新智能体状态成功，ID: {}, 状态: {}", id, status);
        }

        return result;
    }

    /**
     * 加密密码
     */
    private String encryptPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return null;
        }

        try {
            // 使用简单的Base64编码，实际项目中应该使用更安全的加密方式
            return java.util.Base64.getEncoder().encodeToString(password.getBytes("UTF-8"));
        } catch (Exception e) {
            log.error("密码加密失败", e);
            throw new BusinessException("密码加密失败");
        }
    }

    /**
     * 解密密码
     */
    private String decryptPassword(String encryptedPassword) {
        if (!StringUtils.hasText(encryptedPassword)) {
            return null;
        }

        try {
            // 使用简单的Base64解码，实际项目中应该使用更安全的解密方式
            return new String(java.util.Base64.getDecoder().decode(encryptedPassword), "UTF-8");
        } catch (Exception e) {
            log.error("密码解密失败", e);
            throw new BusinessException("密码解密失败");
        }
    }

    /**
     * 批量填充外部智能体详细信息（性能优化版本）
     */
    private void fillExternalAgentDetailsBatch(List<AgentVO> agentVOs) {
        if (agentVOs == null || agentVOs.isEmpty()) {
            return;
        }

        // 筛选出需要查询第三方智能体信息的记录
        List<AgentVO> externalAgents = agentVOs.stream()
                .filter(agent -> "external".equals(agent.getSourceType())
                && StringUtils.hasText(agent.getExternalAgentId()))
                .collect(java.util.stream.Collectors.toList());

        if (externalAgents.isEmpty()) {
            return;
        }

        try {
            // 提取所有需要查询的第三方智能体ID
            List<String> externalAgentIds = externalAgents.stream()
                    .map(AgentVO::getExternalAgentId)
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());

            // 批量查询第三方智能体信息
            List<ThirdPlatform> thirdPlatforms
                    = thirdPlatformMapper.selectByIds(externalAgentIds);

            // 构建ID到实体的映射，便于快速查找
            Map<String, ThirdPlatform> thirdPlatformMap
                    = thirdPlatforms.stream()
                            .collect(java.util.stream.Collectors.toMap(
                                    ThirdPlatform::getId,
                                    agent -> agent,
                                    (existing, replacement) -> existing));

            // 使用并发流填充信息
            if (externalAgents.size() > 5) {
                externalAgents.parallelStream().forEach(agentVO
                        -> fillSingleExternalAgentDetails(agentVO, thirdPlatformMap));
            } else {
                externalAgents.forEach(agentVO
                        -> fillSingleExternalAgentDetails(agentVO, thirdPlatformMap));
            }

        } catch (Exception e) {
            log.error("批量填充外部智能体详细信息失败", e);
            // 降级到单个查询
            externalAgents.forEach(this::fillExternalAgentDetails);
        }
    }

    /**
     * 填充单个外部智能体详细信息（从缓存映射中获取）
     */
    private void fillSingleExternalAgentDetails(AgentVO agentVO,
            Map<String, ThirdPlatform> thirdPlatformMap) {

        ThirdPlatform thirdPlatform
                = thirdPlatformMap.get(agentVO.getExternalAgentId());

        if (thirdPlatform != null) {
            // 设置第三方智能体信息
            agentVO.setThirdPlatform(thirdPlatform);
            // 设置外部智能体名称
            agentVO.setExternalAgentName(thirdPlatform.getName());

            // 构建外部智能体详细信息JSON
            buildExternalAgentDetailsJson(agentVO, thirdPlatform);
        } else {
            log.debug("外部智能体 {} 在 third_platform 表中没有找到对应记录", agentVO.getExternalAgentId());
        }
    }

    /**
     * 填充外部智能体详细信息（单个查询版本，用于降级）
     */
    private void fillExternalAgentDetails(AgentVO agentVO) {
        if (agentVO == null || !"external".equals(agentVO.getSourceType())
                || !StringUtils.hasText(agentVO.getExternalAgentId())) {
            return;
        }

        // 手动查询第三方智能体信息
        try {
            if (StringUtils.hasText(agentVO.getExternalAgentId())) {
                // 根据 external_agent_id 查询第三方智能体
                ThirdPlatform thirdPlatform
                        = thirdPlatformMapper.selectById(agentVO.getExternalAgentId());

                if (thirdPlatform != null) {
                    // 设置第三方智能体信息
                    agentVO.setThirdPlatform(thirdPlatform);
                    // 设置外部智能体名称
                    agentVO.setExternalAgentName(thirdPlatform.getName());
                } else {
                    log.debug("外部智能体 {} 在 third_platform 表中没有找到对应记录", agentVO.getExternalAgentId());
                }
            }
        } catch (Exception e) {
            log.warn("查询第三方智能体信息失败: agentId={}, externalAgentId={}, error={}",
                    agentVO.getId(), agentVO.getExternalAgentId(), e.getMessage());
        }

        // 构建简化的外部智能体详细信息JSON（用于向后兼容）
        // 注意：不再在这里处理模型配置信息，modelConfig字段应该保持原样
        if (agentVO.getThirdPlatform() != null) {
            try {
                Map<String, Object> details = new HashMap<>();
                ThirdPlatform tpa = agentVO.getThirdPlatform();

                // 基本信息
                details.put("platform", tpa.getName() != null ? tpa.getName() : "未知平台");
                details.put("externalId", agentVO.getExternalAgentId());

                // 连接状态
                details.put("status", tpa.getStatus() != null && tpa.getStatus() == 1 ? "connected" : "disconnected");

                // 时间信息
                if (tpa.getUpdateTime() != null) {
                    details.put("lastSync", tpa.getUpdateTime().toString());
                } else if (agentVO.getUpdateTime() != null) {
                    details.put("lastSync", agentVO.getUpdateTime().toString());
                }

                // 第三方智能体特有信息
                details.put("connectionUrl", tpa.getConnectionUrl());
                // 注意：ThirdPlatform 实体可能没有 unitName 字段，需要通过关联查询获取
                details.put("accessScope", tpa.getAccessScope());

                // 测试信息
                if (tpa.getLastTestTime() != null) {
                    details.put("lastTestTime", tpa.getLastTestTime().toString());
                }
                if (tpa.getLastTestResult() != null) {
                    String testResultText = tpa.getLastTestResult() == 1 ? "成功" : "失败";
                    details.put("lastTestResult", testResultText);
                }
                if (tpa.getLastTestError() != null) {
                    details.put("lastTestError", tpa.getLastTestError());
                }

                // 配置信息
                Map<String, Object> config = new HashMap<>();
                if (tpa.getTimeout() != null) {
                    config.put("timeout", tpa.getTimeout());
                }
                if (!config.isEmpty()) {
                    details.put("config", config);
                }

                agentVO.setExternalAgentDetails(JsonUtils.toJsonString(details));

            } catch (Exception e) {
                log.debug("构建外部智能体详细信息JSON失败: {}", e.getMessage());

                // 设置错误信息
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("platform", "未知平台");
                errorDetails.put("status", "error");
                errorDetails.put("error", "获取详细信息失败: " + e.getMessage());
                agentVO.setExternalAgentDetails(JsonUtils.toJsonString(errorDetails));
            }
        }
    }

    /**
     * 构建外部智能体详细信息JSON
     */
    private void buildExternalAgentDetailsJson(AgentVO agentVO,
            ThirdPlatform thirdPlatform) {
        try {
            Map<String, Object> details = new HashMap<>();

            // 基本信息
            details.put("platform", thirdPlatform.getName() != null ? thirdPlatform.getName() : "未知平台");
            details.put("externalId", agentVO.getExternalAgentId());

            // 连接状态
            details.put("status", thirdPlatform.getStatus() != null && thirdPlatform.getStatus() == 1 ? "connected" : "disconnected");

            // 时间信息
            if (thirdPlatform.getUpdateTime() != null) {
                details.put("lastSync", thirdPlatform.getUpdateTime().toString());
            } else if (agentVO.getUpdateTime() != null) {
                details.put("lastSync", agentVO.getUpdateTime().toString());
            }

            // 第三方智能体特有信息
            details.put("connectionUrl", thirdPlatform.getConnectionUrl());
            details.put("accessScope", thirdPlatform.getAccessScope());

            // 测试信息
            if (thirdPlatform.getLastTestTime() != null) {
                details.put("lastTestTime", thirdPlatform.getLastTestTime().toString());
            }
            if (thirdPlatform.getLastTestResult() != null) {
                String testResultText = thirdPlatform.getLastTestResult() == 1 ? "成功" : "失败";
                details.put("lastTestResult", testResultText);
            }
            if (thirdPlatform.getLastTestError() != null) {
                details.put("lastTestError", thirdPlatform.getLastTestError());
            }

            // 配置信息
            Map<String, Object> config = new HashMap<>();
            if (thirdPlatform.getTimeout() != null) {
                config.put("timeout", thirdPlatform.getTimeout());
            }
            if (!config.isEmpty()) {
                details.put("config", config);
            }

            agentVO.setExternalAgentDetails(JsonUtils.toJsonString(details));

        } catch (Exception e) {
            log.debug("构建外部智能体详细信息JSON失败: {}", e.getMessage());

            // 设置错误信息
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("platform", "未知平台");
            errorDetails.put("status", "error");
            errorDetails.put("error", "获取详细信息失败: " + e.getMessage());
            agentVO.setExternalAgentDetails(JsonUtils.toJsonString(errorDetails));
        }
    }

    @Override
    @Transactional
    public boolean linkAgentsToProject(List<String> agentIds, String projectId) {
        if (agentIds == null || agentIds.isEmpty() || !StringUtils.hasText(projectId)) {
            throw new BusinessException("智能体ID列表和项目ID不能为空");
        }

        try {
            // 批量更新智能体的项目关联
            for (String agentId : agentIds) {
                Agent agent = getById(agentId);
                if (agent == null) {
                    log.warn("智能体不存在: {}", agentId);
                    continue;
                }

                // 检查智能体是否已关联其他项目
                if (StringUtils.hasText(agent.getProject_id()) && !projectId.equals(agent.getProject_id())) {
                    log.warn("智能体 {} 已关联到项目 {}", agentId, agent.getProject_id());
                    continue;
                }

                agent.setProject_id(projectId);
                agent.setUpdateTime(LocalDateTime.now());
                updateById(agent);
            }

            return true;
        } catch (Exception e) {
            log.error("关联智能体到项目失败", e);
            throw new BusinessException("关联智能体到项目失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<AgentVO> getUnlinkedAgents(AgentQueryDTO queryDTO) {
        try {
            // 设置租户ID
            if (queryDTO.getTenantId() == null) {
                queryDTO.setTenantId(TenantContextHolder.getTenantId());
            }

            // 创建分页对象
            Page<Agent> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

            // 查询未关联项目的智能体
            IPage<Agent> agentPage = baseMapper.selectUnlinkedAgents(page, queryDTO);

            // 转换为VO
            List<AgentVO> agentVOs = agentPage.getRecords().stream()
                    .map(agent -> {
                        AgentVO vo = new AgentVO();
                        BeanUtils.copyProperties(agent, vo);
                        return vo;
                    })
                    .toList();

            return new PageResult<>(agentVOs, agentPage.getTotal(), agentPage.getCurrent(), agentPage.getSize());
        } catch (Exception e) {
            log.error("查询未关联项目的智能体失败", e);
            throw new BusinessException("查询未关联项目的智能体失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createThirdPartyAgent(ThirdPartyAgentCreateDTO createDTO) {
        log.info("创建第三方智能体: {}", createDTO);

        try {
            // 获取当前用户ID
            String userId = SecurityUtils.getCurrentUserId();
            if (userId == null || userId.trim().isEmpty()) {
                throw new BusinessException("用户未登录，无法创建第三方智能体");
            }

            // 1. 调用第三方平台创建智能体
            DifyAgentCreateRequestDTO dto = new DifyAgentCreateRequestDTO();
            BeanUtils.copyProperties(createDTO, dto);

            // 处理响应式返回值，传递用户上下文
            Mono<Result<DifyAgentCreateResponseDTO>> resultMono = iDifyAgentService.createThirdPartyAgent(dto)
                    .contextWrite(context -> context.put("userId", userId));
            Result<DifyAgentCreateResponseDTO> result = resultMono.block(); // 阻塞等待结果
        
            if (result == null || !result.isSuccess()) {
                String errorMsg = result != null ? result.getMessage() : "第三方平台无响应";
                throw new BusinessException("第三方平台创建智能体失败: " + errorMsg);
            }
        
            DifyAgentCreateResponseDTO responseDTO = result.getData();
            if (responseDTO == null || !StringUtils.hasText(responseDTO.getId())) {
                throw new BusinessException("第三方平台创建智能体失败，未返回有效ID");
            }
        
            String externalAgentId = responseDTO.getId();

            // 2. 创建本地智能体记录
            Agent agent = new Agent();
            agent.setName(createDTO.getName());
            agent.setDescription(createDTO.getDescription());
            agent.setType(createDTO.getMode());
            agent.setSourceType("external");
            agent.setIsPublic(false);
            agent.setSortOrder(0);
            agent.setVersion("1.0.0");
            agent.setConversationCount(0L);
            agent.setExternalAgentId(externalAgentId);
            agent.setPlatformId(createDTO.getPlatformId());
            agent.setStatus("0");//未发布
            // 设置图标相关信息
            agent.setAvatar(createDTO.getAvatar());
            agent.setIconBackground(createDTO.getIconBackground());

            // 保存到数据库
            if (!save(agent)) {
                throw new BusinessException("创建智能体失败");
            }

            log.info("创建第三方智能体成功，ID: {}, 名称: {}", agent.getId(), createDTO.getName());
            return agent.getId();
        } catch (BusinessException e) {
            log.error("创建第三方智能体业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建第三方智能体失败", e);
            throw new BusinessException("创建第三方智能体失败: " + e.getMessage());
        }
    }

    @Override
    public String cloneThirdPartyAgent(ThirdPartyAgentCloneDTO cloneDTO) {
        log.info("克隆第三方智能体: {}", cloneDTO);

        try {
            // 获取当前用户ID
            String userId = SecurityUtils.getCurrentUserId();
            if (userId == null || userId.trim().isEmpty()) {
                throw new BusinessException("用户未登录，无法克隆第三方智能体");
            }

            // 1. 调用第三方平台克隆智能体
            DifyAgentCloneRequestDTO dto = new DifyAgentCloneRequestDTO();
            BeanUtils.copyProperties(cloneDTO, dto);

            // 处理响应式返回值，传递用户上下文
            Mono<Result<DifyAgentCreateResponseDTO>> resultMono = iDifyAgentService.cloneThirdPartyAgent(dto)
                    .contextWrite(context -> context.put("userId", userId));
            Result<DifyAgentCreateResponseDTO> result = resultMono.block(); // 阻塞等待结果

            if (result == null || !result.isSuccess()) {
                String errorMsg = result != null ? result.getMessage() : "第三方平台无响应";
                throw new BusinessException("第三方平台克隆智能体失败: " + errorMsg);
            }

            DifyAgentCreateResponseDTO responseDTO = result.getData();
            if (responseDTO == null || !StringUtils.hasText(responseDTO.getId())) {
                throw new BusinessException("第三方平台克隆智能体失败，未返回有效ID");
            }

            String externalAgentId = responseDTO.getId();

            // 2. 创建本地智能体记录
            Agent agent = new Agent();
            agent.setName(cloneDTO.getName());
            agent.setDescription(responseDTO.getDescription());
            agent.setType(cloneDTO.getMode());
            agent.setSourceType("external");
            agent.setIsPublic(false);
            agent.setSortOrder(0);
            agent.setVersion("1.0.0");
            agent.setConversationCount(0L);
            agent.setExternalAgentId(externalAgentId);
            agent.setPlatformId(cloneDTO.getPlatformId());
            agent.setStatus("0");//未发布
            // 设置图标相关信息
            agent.setAvatar(cloneDTO.getAvatar());
            agent.setIconBackground(cloneDTO.getIconBackground());
            agent.setDescription(cloneDTO.getDescription());

            // 保存到数据库
            if (!save(agent)) {
                throw new BusinessException("创建智能体失败");
            }

            log.info("克隆第三方智能体成功，ID: {}, 名称: {}", agent.getId(), cloneDTO.getName());
            return agent.getId();
        } catch (BusinessException e) {
            log.error("克隆第三方智能体业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("克隆第三方智能体失败", e);
            throw new BusinessException("克隆第三方智能体失败: " + e.getMessage());
        }
    }
}
