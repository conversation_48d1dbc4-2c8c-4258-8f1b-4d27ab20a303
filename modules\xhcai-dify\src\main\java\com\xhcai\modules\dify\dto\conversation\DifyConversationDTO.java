package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * Dify 会话信息 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify 会话信息")
public class DifyConversationDTO {

    @Schema(description = "会话ID")
    private String id;

    @Schema(description = "会话名称")
    private String name;

    @Schema(description = "输入参数")
    private Map<String, Object> inputs;

    @Schema(description = "会话状态")
    private String status;

    @Schema(description = "会话介绍")
    private String introduction;

    @Schema(description = "创建时间戳")
    @JsonProperty("created_at")
    private Long createdAt;

    @Schema(description = "更新时间戳")
    @JsonProperty("updated_at")
    private Long updatedAt;

    @Schema(description = "会话摘要")
    private String summary;

    @Schema(description = "来源")
    @JsonProperty("from_source")
    private String fromSource;

    @Schema(description = "来源终端用户ID")
    @JsonProperty("from_end_user_id")
    private String fromEndUserId;

    @Schema(description = "来源终端用户会话ID")
    @JsonProperty("from_end_user_session_id")
    private String fromEndUserSessionId;

    @Schema(description = "来源账户ID")
    @JsonProperty("from_account_id")
    private String fromAccountId;

    @Schema(description = "来源账户名称")
    @JsonProperty("from_account_name")
    private String fromAccountName;

    @Schema(description = "阅读时间戳")
    @JsonProperty("read_at")
    private Long readAt;

    @Schema(description = "是否已标注")
    private Boolean annotated;

    @Schema(description = "模型配置")
    @JsonProperty("model_config")
    private Map<String, Object> modelConfig;

    @Schema(description = "消息数量")
    @JsonProperty("message_count")
    private Integer messageCount;

    @Schema(description = "用户反馈统计")
    @JsonProperty("user_feedback_stats")
    private Map<String, Integer> userFeedbackStats;

    @Schema(description = "管理员反馈统计")
    @JsonProperty("admin_feedback_stats")
    private Map<String, Integer> adminFeedbackStats;

    @Schema(description = "状态统计")
    @JsonProperty("status_count")
    private Map<String, Integer> statusCount;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getFromEndUserId() {
        return fromEndUserId;
    }

    public void setFromEndUserId(String fromEndUserId) {
        this.fromEndUserId = fromEndUserId;
    }

    public String getFromEndUserSessionId() {
        return fromEndUserSessionId;
    }

    public void setFromEndUserSessionId(String fromEndUserSessionId) {
        this.fromEndUserSessionId = fromEndUserSessionId;
    }

    public String getFromAccountId() {
        return fromAccountId;
    }

    public void setFromAccountId(String fromAccountId) {
        this.fromAccountId = fromAccountId;
    }

    public String getFromAccountName() {
        return fromAccountName;
    }

    public void setFromAccountName(String fromAccountName) {
        this.fromAccountName = fromAccountName;
    }

    public Long getReadAt() {
        return readAt;
    }

    public void setReadAt(Long readAt) {
        this.readAt = readAt;
    }

    public Boolean getAnnotated() {
        return annotated;
    }

    public void setAnnotated(Boolean annotated) {
        this.annotated = annotated;
    }

    public Map<String, Object> getModelConfig() {
        return modelConfig;
    }

    public void setModelConfig(Map<String, Object> modelConfig) {
        this.modelConfig = modelConfig;
    }

    public Integer getMessageCount() {
        return messageCount;
    }

    public void setMessageCount(Integer messageCount) {
        this.messageCount = messageCount;
    }

    public Map<String, Integer> getUserFeedbackStats() {
        return userFeedbackStats;
    }

    public void setUserFeedbackStats(Map<String, Integer> userFeedbackStats) {
        this.userFeedbackStats = userFeedbackStats;
    }

    public Map<String, Integer> getAdminFeedbackStats() {
        return adminFeedbackStats;
    }

    public void setAdminFeedbackStats(Map<String, Integer> adminFeedbackStats) {
        this.adminFeedbackStats = adminFeedbackStats;
    }

    public Map<String, Integer> getStatusCount() {
        return statusCount;
    }

    public void setStatusCount(Map<String, Integer> statusCount) {
        this.statusCount = statusCount;
    }

    @Override
    public String toString() {
        return "DifyConversationDTO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", inputs=" + inputs +
                ", status='" + status + '\'' +
                ", introduction='" + introduction + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", summary='" + summary + '\'' +
                ", fromSource='" + fromSource + '\'' +
                ", fromEndUserId='" + fromEndUserId + '\'' +
                ", fromEndUserSessionId='" + fromEndUserSessionId + '\'' +
                ", fromAccountId='" + fromAccountId + '\'' +
                ", fromAccountName='" + fromAccountName + '\'' +
                ", readAt=" + readAt +
                ", annotated=" + annotated +
                ", modelConfig=" + modelConfig +
                ", messageCount=" + messageCount +
                ", userFeedbackStats=" + userFeedbackStats +
                ", adminFeedbackStats=" + adminFeedbackStats +
                ", statusCount=" + statusCount +
                '}';
    }
}
