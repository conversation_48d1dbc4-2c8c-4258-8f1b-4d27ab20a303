package com.xhcai.modules.rag.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.rag.entity.DatasetCategory;
import com.xhcai.modules.rag.vo.DatasetCategoryVO;

/**
 * 文档分类Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface DatasetCategoryMapper extends BaseMapper<DatasetCategory> {

    /**
     * 根据父分类ID查询子分类
     */
    @Select("<script>"
            + "SELECT * FROM dataset_categories "
            + "WHERE tenant_id = #{tenantId} "
            + "AND deleted = 0 "
            + "<if test='parentId != null'>"
            + "AND parent_id = #{parentId} "
            + "</if>"
            + "<if test='parentId == null'>"
            + "AND parent_id IS NULL "
            + "</if>"
            + "ORDER BY sort_order ASC, create_time ASC"
            + "</script>")
    List<DatasetCategory> queryList(@Param("tenantId") String tenantId, @Param("parentId") String parentId);

    /**
     * 根据父分类ID查询子分类
     */
    @Select("<script>"
            + "SELECT * FROM dataset_categories "
            + "WHERE tenant_id = #{tenantId} "
            + "AND deleted = 0 "
            + "<if test='datasetId != null'>"
            + "AND dataset_id = #{datasetId} "
            + "</if>"
            + "<if test='parentId != null'>"
            + "AND parent_id = #{parentId} "
            + "</if>"
            + "<if test='parentId == null'>"
            + "AND parent_id IS NULL "
            + "</if>"
            + "ORDER BY sort_order ASC, create_time ASC"
            + "</script>")
    List<DatasetCategory> selectByParentId(@Param("tenantId") String tenantId, @Param("datasetId") String datasetId, @Param("parentId") String parentId);

    /**
     * 更新分类文件数量
     */
    @Update("UPDATE dataset_categories SET file_count = #{fileCount} WHERE id = #{categoryId} AND tenant_id = #{tenantId}")
    int updateFileCount(@Param("tenantId") String tenantId, @Param("categoryId") String categoryId, @Param("fileCount") Integer fileCount);

    /**
     * 查询分类及其所有子分类ID
     */
    @Select("<script>"
            + "WITH RECURSIVE category_tree AS ("
            + "  SELECT id, parent_id, level FROM dataset_categories "
            + "  WHERE id = #{categoryId} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "  UNION ALL "
            + "  SELECT c.id, c.parent_id, c.level FROM dataset_categories c "
            + "  INNER JOIN category_tree ct ON c.parent_id = ct.id "
            + "  WHERE c.tenant_id = #{tenantId} AND c.deleted = 0"
            + ") "
            + "SELECT id FROM category_tree"
            + "</script>")
    List<String> selectCategoryAndChildrenIds(@Param("tenantId") String tenantId, @Param("categoryId") String categoryId);

    /**
     * 查询最大排序号
     */
    @Select("<script>"
            + "SELECT COALESCE(MAX(sort_order), 0) FROM dataset_categories "
            + "WHERE tenant_id = #{tenantId} "
            + "AND deleted = 0 "
            + "<if test='parentId != null'>"
            + "AND parent_id = #{parentId} "
            + "</if>"
            + "<if test='parentId == null'>"
            + "AND parent_id IS NULL "
            + "</if>"
            + "</script>")
    Integer selectMaxSortOrder(@Param("tenantId") String tenantId, @Param("parentId") String parentId);

    /**
     * 获取带文件数统计的分类树
     */
    @Select("<script>"
            + "SELECT "
            + "  dc.id, "
            + "  dc.dataset_id, "
            + "  dc.name, "
            + "  dc.description, "
            + "  dc.parent_id, "
            + "  dc.level, "
            + "  dc.sort_order, "
            + "  dc.enabled, "
            + "  dc.create_time, "
            + "  dc.update_time, "
            + "  COALESCE(doc_count.file_count, 0) as file_count "
            + "FROM dataset_categories dc "
            + "LEFT JOIN ( "
            + "  SELECT category_id, COUNT(*) as file_count "
            + "  FROM documents "
            + "  WHERE tenant_id = #{tenantId} "
            + "  AND deleted = 0 "
            + "  <if test='datasetId != null'>"
            + "  AND dataset_id = #{datasetId} "
            + "  </if>"
            + "  GROUP BY category_id "
            + ") doc_count ON dc.id = doc_count.category_id "
            + "WHERE dc.tenant_id = #{tenantId} "
            + "AND dc.deleted = 0 "
            + "AND dc.enabled = true "
            + "<if test='datasetId != null'>"
            + "AND dc.dataset_id = #{datasetId} "
            + "</if>"
            + "ORDER BY dc.level ASC, dc.sort_order ASC, dc.create_time ASC"
            + "</script>")
    List<DatasetCategoryVO> selectCategoryTreeWithFileCount(@Param("tenantId") String tenantId, @Param("datasetId") String datasetId);
}
