package com.xhcai.modules.agent.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 智能体会话记录查询DTO
 *
 * 请求参数：智能体ID、页码(默认1)、每页数量(默认10)、开始时间、结束时间、排序字段(默认create_time)、排序方向(默认desc)、关键词(keyword-匹配title)
 */
@Schema(description = "智能体会话记录查询DTO")
public class AgentConversationQueryDTO extends PageTimeRangeQueryDTO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "智能体ID", example = "agent_001", required = true)
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 关键词（按会话标题title模糊匹配）
     */
    @Schema(description = "关键词（匹配会话标题）", example = "咨询")
    @Size(max = 100, message = "关键词长度不能超过100个字符")
    private String keyword;

    /**
     * 排序字段，支持：create_time, last_activity_at, started_at, ended_at
     * 默认：create_time
     */
    @Schema(description = "排序字段", example = "create_time")
    private String orderBy = "create_time";

    /**
     * 排序方向，asc/desc，默认desc
     */
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String orderDirection = "desc";

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String getOrderBy() {
        return orderBy;
    }

    @Override
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    @Override
    public String getOrderDirection() {
        return orderDirection;
    }

    @Override
    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }
}
