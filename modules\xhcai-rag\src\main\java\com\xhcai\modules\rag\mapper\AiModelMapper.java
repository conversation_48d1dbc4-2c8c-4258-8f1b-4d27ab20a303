package com.xhcai.modules.rag.mapper;

import java.util.List;

import com.xhcai.modules.system.handler.SysDictDataTypeHandler;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.rag.dto.AiModelQueryDTO;
import com.xhcai.modules.rag.entity.AiModel;
import com.xhcai.modules.rag.vo.AiModelVO;

/**
 * AI模型配置Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AiModelMapper extends BaseMapper<AiModel> {

    /**
     * 分页查询AI模型列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Results(id = "AiModelPageWithDictResultMap", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "name", property = "name"),
        @Result(column = "model_id", property = "modelId"),
        @Result(column = "provider", property = "provider"),
        @Result(column = "type", property = "type"),
        @Result(column = "platform", property = "platform"),
        @Result(column = "version", property = "version"),
        @Result(column = "description", property = "description"),
        @Result(column = "status", property = "status"),
        @Result(column = "api_endpoint", property = "apiEndpoint"),
        @Result(column = "organization_id", property = "organizationId"),
        @Result(column = "timeout", property = "timeout"),
        @Result(column = "max_tokens", property = "maxTokens"),
        @Result(column = "temperature", property = "temperature"),
        @Result(column = "top_p", property = "topP"),
        @Result(column = "frequency_penalty", property = "frequencyPenalty"),
        @Result(column = "presence_penalty", property = "presencePenalty"),
        @Result(column = "stop_sequences", property = "stopSequences"),
        @Result(column = "input_price", property = "inputPrice"),
        @Result(column = "output_price", property = "outputPrice"),
        @Result(column = "rpm_limit", property = "rpmLimit"),
        @Result(column = "tpm_limit", property = "tpmLimit"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "remark", property = "remark"),
        @Result(column = "create_by", property = "createBy"),
        @Result(column = "create_time", property = "createTime"),
        @Result(column = "update_by", property = "updateBy"),
        @Result(column = "update_time", property = "updateTime"),
        @Result(column = "provider_dict", property = "providerDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "type_dict", property = "typeDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "platform_dict", property = "platformDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    @Select("<script>"
            + "SELECT "
            + "    m.id, m.name, m.model_id, m.provider, m.type, m.platform, m.version, m.description, m.status, "
            + "    m.api_endpoint, m.organization_id, m.timeout, m.max_tokens, m.temperature, "
            + "    m.top_p, m.frequency_penalty, m.presence_penalty, m.stop_sequences, "
            + "    m.input_price, m.output_price, m.rpm_limit, m.tpm_limit, "
            + "    m.tenant_id, m.remark, m.create_by, m.create_time, m.update_by, m.update_time, "
            + "    to_jsonb(pd.*) as provider_dict, "
            + "    to_jsonb(td.*) as type_dict, "
            + "    to_jsonb(pld.*) as platform_dict "
            + "FROM models m "
            + "LEFT JOIN sys_dict_data pd ON pd.dict_value = m.provider AND pd.dict_type = 'ai_provider' AND pd.deleted = 0 "
            + "LEFT JOIN sys_dict_data td ON td.dict_value = m.type AND td.dict_type = 'ai_model_type' AND td.deleted = 0 "
            + "LEFT JOIN sys_dict_data pld ON pld.dict_value = m.platform AND pld.dict_type = 'model_inference_platform' AND pld.deleted = 0 "
            + "WHERE m.deleted = 0 "
            + "<if test='queryDTO.name != null and queryDTO.name != &quot;&quot;'>"
            + "    AND m.name LIKE CONCAT('%', #{queryDTO.name}, '%') "
            + "</if>"
            + "<if test='queryDTO.modelId != null and queryDTO.modelId != &quot;&quot;'>"
            + "    AND m.model_id LIKE CONCAT('%', #{queryDTO.modelId}, '%') "
            + "</if>"
            + "<if test='queryDTO.provider != null and queryDTO.provider != &quot;&quot;'>"
            + "    AND m.provider = #{queryDTO.provider} "
            + "</if>"
            + "<if test='queryDTO.type != null and queryDTO.type != &quot;&quot;'>"
            + "    AND m.type IN "
            + "  <foreach collection='@java.util.Arrays@asList(queryDTO.type.split(\",\"))' item='item' open='(' separator=',' close=')'>"
            + "    #{item}"
            + "  </foreach>"
            + "</if>"
            + "<if test='queryDTO.status != null and queryDTO.status != &quot;&quot;'>"
            + "    AND m.status = #{queryDTO.status} "
            + "</if>"
            + "<if test='queryDTO.beginTime != null and queryDTO.beginTime != &quot;&quot;'>"
            + "    AND m.create_time &gt;= #{queryDTO.beginTime} "
            + "</if>"
            + "<if test='queryDTO.endTime != null and queryDTO.endTime != &quot;&quot;'>"
            + "    AND m.create_time &lt;= #{queryDTO.endTime} "
            + "</if>"
            + "ORDER BY "
            + "<choose>"
            + "    <when test='queryDTO.orderBy != null and queryDTO.orderBy != &quot;&quot;'>"
            + "        m.${queryDTO.orderBy} "
            + "        <if test='queryDTO.orderDirection != null and queryDTO.orderDirection != &quot;&quot;'>"
            + "            ${queryDTO.orderDirection}"
            + "        </if>"
            + "    </when>"
            + "    <otherwise>"
            + "        m.create_time DESC"
            + "    </otherwise>"
            + "</choose>"
            + "</script>")
    IPage<AiModelVO> selectAiModelPage(Page<AiModelVO> page, @Param("queryDTO") AiModelQueryDTO queryDTO);

    /**
     * 查询AI模型列表
     *
     * @param queryDTO 查询条件
     * @return AI模型列表
     */
    @Results(id = "AiModelListWithDictResultMap", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "name", property = "name"),
        @Result(column = "model_id", property = "modelId"),
        @Result(column = "provider", property = "provider"),
        @Result(column = "type", property = "type"),
        @Result(column = "platform", property = "platform"),
        @Result(column = "version", property = "version"),
        @Result(column = "description", property = "description"),
        @Result(column = "status", property = "status"),
        @Result(column = "api_endpoint", property = "apiEndpoint"),
        @Result(column = "organization_id", property = "organizationId"),
        @Result(column = "timeout", property = "timeout"),
        @Result(column = "max_tokens", property = "maxTokens"),
        @Result(column = "temperature", property = "temperature"),
        @Result(column = "top_p", property = "topP"),
        @Result(column = "frequency_penalty", property = "frequencyPenalty"),
        @Result(column = "presence_penalty", property = "presencePenalty"),
        @Result(column = "stop_sequences", property = "stopSequences"),
        @Result(column = "input_price", property = "inputPrice"),
        @Result(column = "output_price", property = "outputPrice"),
        @Result(column = "rpm_limit", property = "rpmLimit"),
        @Result(column = "tpm_limit", property = "tpmLimit"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "remark", property = "remark"),
        @Result(column = "create_by", property = "createBy"),
        @Result(column = "create_time", property = "createTime"),
        @Result(column = "update_by", property = "updateBy"),
        @Result(column = "update_time", property = "updateTime"),
        @Result(column = "provider_dict", property = "providerDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "type_dict", property = "typeDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "platform_dict", property = "platformDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    @Select("<script>"
            + "SELECT "
            + "    m.id, m.name, m.model_id, m.provider, m.type, m.platform, m.version, m.description, m.status, "
            + "    m.api_endpoint, m.organization_id, m.timeout, m.max_tokens, m.temperature, "
            + "    m.top_p, m.frequency_penalty, m.presence_penalty, m.stop_sequences, "
            + "    m.input_price, m.output_price, m.rpm_limit, m.tpm_limit, "
            + "    m.tenant_id, m.remark, m.create_by, m.create_time, m.update_by, m.update_time, "
            + "    to_jsonb(pd.*) as provider_dict, "
            + "    to_jsonb(td.*) as type_dict, "
            + "    to_jsonb(pld.*) as platform_dict "
            + "FROM models m "
            + "LEFT JOIN sys_dict_data pd ON pd.dict_value = m.provider AND pd.dict_type = 'ai_provider' AND pd.deleted = 0 "
            + "LEFT JOIN sys_dict_data td ON td.dict_value = m.type AND td.dict_type = 'ai_model_type' AND td.deleted = 0 "
            + "LEFT JOIN sys_dict_data pld ON pld.dict_value = m.platform AND pld.dict_type = 'model_inference_platform' AND pld.deleted = 0 "
            + "WHERE m.deleted = 0 "
            + "<if test='name != null and name != &quot;&quot;'>"
            + "    AND m.name LIKE CONCAT('%', #{name}, '%') "
            + "</if>"
            + "<if test='modelId != null and modelId != &quot;&quot;'>"
            + "    AND m.model_id LIKE CONCAT('%', #{modelId}, '%') "
            + "</if>"
            + "<if test='provider != null and provider != &quot;&quot;'>"
            + "    AND m.provider = #{provider} "
            + "</if>"
            + "<if test='type != null and type != &quot;&quot;'>"
            + "    AND m.type IN "
            + "  <foreach collection='@java.util.Arrays@asList(type.split(\",\"))' item='item' open='(' separator=',' close=')'>"
            + "    #{item}"
            + "  </foreach>"
            + "</if>"
            + "<if test='status != null and status != &quot;&quot;'>"
            + "    AND m.status = #{status} "
            + "</if>"
            + "<if test='beginTime != null and beginTime != &quot;&quot;'>"
            + "    AND m.create_time &gt;= #{beginTime} "
            + "</if>"
            + "<if test='endTime != null and endTime != &quot;&quot;'>"
            + "    AND m.create_time &lt;= #{endTime} "
            + "</if>"
            + "ORDER BY "
            + "<choose>"
            + "    <when test='orderBy != null and orderBy != &quot;&quot;'>"
            + "        m.${orderBy} "
            + "        <if test='orderDirection != null and orderDirection != &quot;&quot;'>"
            + "            ${orderDirection}"
            + "        </if>"
            + "    </when>"
            + "    <otherwise>"
            + "        m.create_time DESC"
            + "    </otherwise>"
            + "</choose>"
            + "</script>")
    List<AiModelVO> selectAiModelList(AiModelQueryDTO queryDTO);

    /**
     * 根据ID查询AI模型详情
     *
     * @param id 模型ID
     * @return AI模型详情
     */
    @Results(id = "AiModelWithDictResultMap", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "name", property = "name"),
        @Result(column = "model_id", property = "modelId"),
        @Result(column = "provider", property = "provider"),
        @Result(column = "type", property = "type"),
        @Result(column = "platform", property = "platform"),
        @Result(column = "version", property = "version"),
        @Result(column = "description", property = "description"),
        @Result(column = "status", property = "status"),
        @Result(column = "api_endpoint", property = "apiEndpoint"),
        @Result(column = "api_key", property = "apiKey"),
        @Result(column = "organization_id", property = "organizationId"),
        @Result(column = "timeout", property = "timeout"),
        @Result(column = "max_tokens", property = "maxTokens"),
        @Result(column = "temperature", property = "temperature"),
        @Result(column = "top_p", property = "topP"),
        @Result(column = "frequency_penalty", property = "frequencyPenalty"),
        @Result(column = "presence_penalty", property = "presencePenalty"),
        @Result(column = "stop_sequences", property = "stopSequences"),
        @Result(column = "input_price", property = "inputPrice"),
        @Result(column = "output_price", property = "outputPrice"),
        @Result(column = "rpm_limit", property = "rpmLimit"),
        @Result(column = "tpm_limit", property = "tpmLimit"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "remark", property = "remark"),
        @Result(column = "create_by", property = "createBy"),
        @Result(column = "create_time", property = "createTime"),
        @Result(column = "update_by", property = "updateBy"),
        @Result(column = "update_time", property = "updateTime"),
        @Result(column = "provider_dict", property = "providerDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "type_dict", property = "typeDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "platform_dict", property = "platformDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    @Select("SELECT "
            + "    m.id, m.name, m.model_id, m.provider, m.type, m.platform, m.version, m.description, m.status, "
            + "    m.api_endpoint, m.api_key, m.organization_id, m.timeout, m.max_tokens, m.temperature, "
            + "    m.top_p, m.frequency_penalty, m.presence_penalty, m.stop_sequences, "
            + "    m.input_price, m.output_price, m.rpm_limit, m.tpm_limit, "
            + "    m.tenant_id, m.remark, m.create_by, m.create_time, m.update_by, m.update_time, "
            + "    to_jsonb(pd.*) as provider_dict, "
            + "    to_jsonb(td.*) as type_dict, "
            + "    to_jsonb(pld.*) as platform_dict "
            + "FROM models m "
            + "LEFT JOIN sys_dict_data pd ON pd.dict_value = m.provider AND pd.dict_type = 'ai_provider' AND pd.deleted = 0 "
            + "LEFT JOIN sys_dict_data td ON td.dict_value = m.type AND td.dict_type = 'ai_model_type' AND td.deleted = 0 "
            + "LEFT JOIN sys_dict_data pld ON pld.dict_value = m.platform AND pld.dict_type = 'model_inference_platform' AND pld.deleted = 0 "
            + "WHERE m.id = #{id} AND m.deleted = 0")
    AiModelVO selectAiModelById(@Param("id") String id);

    /**
     * 检查模型标识是否存在
     *
     * @param modelId 模型标识
     * @param excludeId 排除的模型ID
     * @return 存在数量
     */
    @Select("<script>"
            + "SELECT COUNT(1) FROM models "
            + "WHERE model_id = #{modelId} AND deleted = 0 "
            + "<if test='excludeId != null and excludeId != &quot;&quot;'>"
            + "    AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    int checkModelIdExists(@Param("modelId") String modelId, @Param("excludeId") String excludeId);

    /**
     * 检查模型名称是否存在
     *
     * @param name 模型名称
     * @param excludeId 排除的模型ID
     * @return 存在数量
     */
    @Select("<script>"
            + "SELECT COUNT(1) FROM models "
            + "WHERE name = #{name} AND deleted = 0 "
            + "<if test='excludeId != null and excludeId != &quot;&quot;'>"
            + "    AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    int checkModelNameExists(@Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 根据提供商查询模型列表
     *
     * @param provider 提供商
     * @return 模型列表
     */
    @Results(id = "AiModelByProviderWithDictResultMap", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "name", property = "name"),
        @Result(column = "model_id", property = "modelId"),
        @Result(column = "provider", property = "provider"),
        @Result(column = "type", property = "type"),
        @Result(column = "platform", property = "platform"),
        @Result(column = "version", property = "version"),
        @Result(column = "description", property = "description"),
        @Result(column = "status", property = "status"),
        @Result(column = "api_endpoint", property = "apiEndpoint"),
        @Result(column = "organization_id", property = "organizationId"),
        @Result(column = "timeout", property = "timeout"),
        @Result(column = "max_tokens", property = "maxTokens"),
        @Result(column = "temperature", property = "temperature"),
        @Result(column = "top_p", property = "topP"),
        @Result(column = "frequency_penalty", property = "frequencyPenalty"),
        @Result(column = "presence_penalty", property = "presencePenalty"),
        @Result(column = "stop_sequences", property = "stopSequences"),
        @Result(column = "input_price", property = "inputPrice"),
        @Result(column = "output_price", property = "outputPrice"),
        @Result(column = "rpm_limit", property = "rpmLimit"),
        @Result(column = "tpm_limit", property = "tpmLimit"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "remark", property = "remark"),
        @Result(column = "create_by", property = "createBy"),
        @Result(column = "create_time", property = "createTime"),
        @Result(column = "update_by", property = "updateBy"),
        @Result(column = "update_time", property = "updateTime"),
        @Result(column = "provider_dict", property = "providerDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "type_dict", property = "typeDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "platform_dict", property = "platformDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    @Select("SELECT "
            + "    m.id, m.name, m.model_id, m.provider, m.type, m.platform, m.version, m.description, m.status, "
            + "    m.api_endpoint, m.organization_id, m.timeout, m.max_tokens, m.temperature, "
            + "    m.top_p, m.frequency_penalty, m.presence_penalty, m.stop_sequences, "
            + "    m.input_price, m.output_price, m.rpm_limit, m.tpm_limit, "
            + "    m.tenant_id, m.remark, m.create_by, m.create_time, m.update_by, m.update_time, "
            + "    to_jsonb(pd.*) as provider_dict, "
            + "    to_jsonb(td.*) as type_dict, "
            + "    to_jsonb(pld.*) as platform_dict "
            + "FROM models m "
            + "LEFT JOIN sys_dict_data pd ON pd.dict_value = m.provider AND pd.dict_type = 'ai_provider' AND pd.deleted = 0 "
            + "LEFT JOIN sys_dict_data td ON td.dict_value = m.type AND td.dict_type = 'ai_model_type' AND td.deleted = 0 "
            + "LEFT JOIN sys_dict_data pld ON pld.dict_value = m.platform AND pld.dict_type = 'model_inference_platform' AND pld.deleted = 0 "
            + "WHERE m.provider = #{provider} AND m.status = '1' AND m.deleted = 0 "
            + "ORDER BY m.create_time DESC")
    List<AiModelVO> selectByProvider(@Param("provider") String provider);

    /**
     * 根据类型查询模型列表
     *
     * @param type 模型类型
     * @return 模型列表
     */
    @Results(id = "AiModelByTypeWithDictResultMap", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "name", property = "name"),
        @Result(column = "model_id", property = "modelId"),
        @Result(column = "provider", property = "provider"),
        @Result(column = "type", property = "type"),
        @Result(column = "platform", property = "platform"),
        @Result(column = "version", property = "version"),
        @Result(column = "description", property = "description"),
        @Result(column = "status", property = "status"),
        @Result(column = "api_endpoint", property = "apiEndpoint"),
        @Result(column = "organization_id", property = "organizationId"),
        @Result(column = "timeout", property = "timeout"),
        @Result(column = "max_tokens", property = "maxTokens"),
        @Result(column = "temperature", property = "temperature"),
        @Result(column = "top_p", property = "topP"),
        @Result(column = "frequency_penalty", property = "frequencyPenalty"),
        @Result(column = "presence_penalty", property = "presencePenalty"),
        @Result(column = "stop_sequences", property = "stopSequences"),
        @Result(column = "input_price", property = "inputPrice"),
        @Result(column = "output_price", property = "outputPrice"),
        @Result(column = "rpm_limit", property = "rpmLimit"),
        @Result(column = "tpm_limit", property = "tpmLimit"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "remark", property = "remark"),
        @Result(column = "create_by", property = "createBy"),
        @Result(column = "create_time", property = "createTime"),
        @Result(column = "update_by", property = "updateBy"),
        @Result(column = "update_time", property = "updateTime"),
        @Result(column = "provider_dict", property = "providerDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "type_dict", property = "typeDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "platform_dict", property = "platformDict",
                typeHandler = SysDictDataTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    @Select("<script>"
            + "SELECT "
            + "    m.id, m.name, m.model_id, m.provider, m.type, m.platform, m.version, m.description, m.status, "
            + "    m.api_endpoint, m.organization_id, m.timeout, m.max_tokens, m.temperature, "
            + "    m.top_p, m.frequency_penalty, m.presence_penalty, m.stop_sequences, "
            + "    m.input_price, m.output_price, m.rpm_limit, m.tpm_limit, "
            + "    m.tenant_id, m.remark, m.create_by, m.create_time, m.update_by, m.update_time, "
            + "    to_jsonb(pd.*) as provider_dict, "
            + "    to_jsonb(td.*) as type_dict, "
            + "    to_jsonb(pld.*) as platform_dict "
            + "FROM models m "
            + "LEFT JOIN sys_dict_data pd ON pd.dict_value = m.provider AND pd.dict_type = 'ai_provider' AND pd.deleted = 0 "
            + "LEFT JOIN sys_dict_data td ON td.dict_value = m.type AND td.dict_type = 'ai_model_type' AND td.deleted = 0 "
            + "LEFT JOIN sys_dict_data pld ON pld.dict_value = m.platform AND pld.dict_type = 'model_inference_platform' AND pld.deleted = 0 "
            + "WHERE m.type IN "
            + "  <foreach collection='@java.util.Arrays@asList(type.split(\",\"))' item='item' open='(' separator=',' close=')'>"
            + "    #{item}"
            + "  </foreach>"
            + " AND m.status = '1' AND m.deleted = 0 "
            + " ORDER BY m.create_time DESC"
            + "</script>")
    List<AiModelVO> selectByType(@Param("type") String type);

    /**
     * 批量更新模型状态
     *
     * @param ids 模型ID列表
     * @param status 状态
     * @return 更新数量
     */
    @Update("<script>"
            + "UPDATE models SET status = #{status}, update_time = NOW() "
            + "WHERE deleted = 0 AND id IN "
            + "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"
            + "    #{id}"
            + "</foreach>"
            + "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status);

    /**
     * 统计各提供商的模型数量
     *
     * @return 统计结果
     */
    @Select("SELECT provider, COUNT(1) as count "
            + "FROM models "
            + "WHERE deleted = 0 "
            + "GROUP BY provider "
            + "ORDER BY count DESC")
    @Results({
        @Result(column = "provider", property = "provider"),
        @Result(column = "count", property = "count")
    })
    List<java.util.Map<String, Object>> countByProvider();

    /**
     * 统计各类型的模型数量
     *
     * @return 统计结果
     */
    @Select("SELECT type, COUNT(1) as count "
            + "FROM models "
            + "WHERE deleted = 0 "
            + "GROUP BY type "
            + "ORDER BY count DESC")
    @Results({
        @Result(column = "type", property = "type"),
        @Result(column = "count", property = "count")
    })
    List<java.util.Map<String, Object>> countByType();
}
