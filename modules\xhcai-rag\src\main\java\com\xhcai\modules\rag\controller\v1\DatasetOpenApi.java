package com.xhcai.modules.rag.controller.v1;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.DatasetQueryDTO;
import com.xhcai.modules.rag.entity.Dataset;
import com.xhcai.modules.rag.service.IDatasetService;
import com.xhcai.modules.rag.service.IOnlyOfficeService;
import com.xhcai.modules.rag.vo.DatasetVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "知识库管理外部开放接口", description = "知识库的创建、查询、更新、删除等操作")
@RestController
@RequestMapping("/v1/datasets")
@Validated
public class DatasetOpenApi {

    private static final Logger log = LoggerFactory.getLogger(com.xhcai.modules.rag.controller.DatasetController.class);

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private IOnlyOfficeService onlyOfficeService;

    @RequiresApiKey(
            allowTokenAuth = false,
            targetTypes = {"knowledge"},
            message = "分页查询知识库列表"
    )
    @Operation(summary = "分页查询知识库列表", description = "根据条件分页查询知识库列表")
    @GetMapping("/page")
    @RequiresPermissions("rag:dataset:list")
    public Result<PageResult<DatasetVO>> page(@Parameter(description = "查询条件") DatasetQueryDTO queryDTO) {
        log.info("分页查询知识库列表: {}", queryDTO);

        IPage<DatasetVO> page = datasetService.pageByTenantWithUserInfo(
                queryDTO.getCurrent(),
                queryDTO.getSize(),
                queryDTO.getTenantId(),
                queryDTO.getName(),
                queryDTO.getDataSourceType()
        );

        PageResult<DatasetVO> pageResult = new PageResult<>(
                page.getRecords(),
                page.getTotal(),
                page.getCurrent(),
                page.getSize()
        );

        return Result.success(pageResult);
    }

    @RequiresApiKey(
            allowTokenAuth = false,
            targetTypes = {"knowledge"},
            message = "查询知识库详情"
    )
    @Operation(summary = "查询知识库详情", description = "根据ID查询知识库详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("rag:dataset:detail")
    public Result<DatasetVO> getById(@Parameter(description = "知识库ID") @PathVariable String id) {
        log.info("查询知识库详情: id={}", id);

        Dataset dataset = datasetService.getDatasetById(id);
        if (dataset == null) {
            return Result.fail("知识库不存在");
        }

        DatasetVO vo = new DatasetVO();
        BeanUtils.copyProperties(dataset, vo);
        return Result.success(vo);
    }

}
