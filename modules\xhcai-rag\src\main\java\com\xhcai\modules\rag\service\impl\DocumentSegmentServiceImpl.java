package com.xhcai.modules.rag.service.impl;

import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;

import cn.hutool.core.bean.BeanUtil;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.rag.dto.BatchSegmentationRequestDTO;
import com.xhcai.modules.rag.entity.*;
import com.xhcai.modules.rag.entity.inner.FileCleanSegmentConfig;
import com.xhcai.modules.rag.mapper.*;
import com.xhcai.modules.rag.service.*;
import com.xhcai.modules.rag.service.vector.IVectorStoreProcessor;
import com.xhcai.modules.rag.vo.DocumentSegmentVO;
import com.xhcai.modules.rag.vo.DocumentVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.plugins.rabbitmq.producer.RabbitMQProducer;
import com.xhcai.modules.rag.service.segmentation.IFileSegmentationProcessor;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * 文档分段服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
public class DocumentSegmentServiceImpl extends ServiceImpl<DocumentSegmentMapper, DocumentSegment> implements IDocumentSegmentService {
    @Autowired
    private DocumentMapper documentMapper;
    @Autowired
    private IDocumentService documentService;
    @Autowired
    private DocumentSegmentMapper documentSegmentMapper;
    @Autowired
    private List<IFileSegmentationProcessor> segmentationProcessors;
    @Autowired
    private IDocumentProcessingService documentProcessingService;
    @Autowired(required = false)
    private RabbitMQProducer rabbitMQProducer;
    @Autowired
    private List<IVectorStoreProcessor> vectorStoreProcessors;
    @Autowired
    private IEmbeddingService embeddingService;
    @Autowired
    private DatasetMapper datasetMapper;
    @Autowired
    private VectorDatabaseMapper  vectorDatabaseMapper;
    @Autowired
    private AiModelMapper aiModelMapper;

    @Override
    public IPage<DocumentSegment> pageByDocument(Long current, Long size, String documentId,
            String datasetId, String status, Boolean enabled) {
        Page<DocumentSegment> page = new Page<>(current, size);
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();

        if (documentId != null) {
            wrapper.eq(DocumentSegment::getDocumentId, documentId);
        }
        if (datasetId != null) {
            wrapper.eq(DocumentSegment::getDatasetId, datasetId);
        }
        if (status != null) {
            wrapper.eq(DocumentSegment::getStatus, status);
        }
        if (enabled != null) {
            wrapper.eq(DocumentSegment::getEnabled, enabled);
        }

        wrapper.orderByAsc(DocumentSegment::getPosition);

        return page(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchStartSegmentation(BatchSegmentationRequestDTO request) {
        try {
            String tenantId = SecurityUtils.getCurrentTenantId();
            String userId = SecurityUtils.getCurrentUserId();

            // 验证文档是否存在且属于当前租户
            List<Document> documents = documentService.listByIds(request.getDocumentIds());
            if (documents.size() != request.getDocumentIds().size()) {
                throw new BusinessException("部分文档不存在或无权限访问");
            }

            // 更新文档状态为等待处理
            for (Document document : documents) {
                document.setDocumentStatus(DocumentStatus.WAITING.getCode());
                document.setProcessingStartedAt(LocalDateTime.now());

                // 更新文档元数据
                FileCleanSegmentConfig fileCleanSegmentConfig = request.getDocConfigs().get(document.getId());
                if (fileCleanSegmentConfig != null) {
                    document.setSegmentConfig(fileCleanSegmentConfig.getSegmentConfig());
                    document.setCleaningConfig(fileCleanSegmentConfig.getCleaningConfig());
                } else {
                    document.setSegmentConfig(request.getFileCleanSegmentConfig().getSegmentConfig());
                    document.setCleaningConfig(request.getFileCleanSegmentConfig().getCleaningConfig());
                }
            }

            // 保存成功后，发送批量分段处理消息到RabbitMQ
            if (documentService.updateBatchById(documents)) {
                if (rabbitMQProducer != null) {
                    documents.forEach(document -> {
                        rabbitMQProducer.sendDocumentSegmentationMessage(document, tenantId, userId);
                    });
                }
            } else {
                log.warn("RabbitMQ生产者未配置，无法发送批量分段处理消息");
                return false;
            }
            return true;

        } catch (Exception e) {
            log.error("批量分段处理启动失败: {}", e.getMessage(), e);
            throw new BusinessException("批量分段处理启动失败: " + e.getMessage());
        }
    }

    /**
     * 处理文档分段（带用户信息）
     *
     * @param document 文档信息
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processDocumentSegmentation(Document document, String tenantId, String userId) {
        try {
            // 0. 清空错误信息
            documentMapper.updateError(document.getId(), DocumentStatus.PROCESSING.getCode(), "");

            // 1. 推送开始处理状态
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.PROCESSING, 0, "开始分段处理...");

            // 2. 更新文档状态为处理中
            documentMapper.updateDocumentStatus(document.getId(), DocumentStatus.SEGMENTING.getCode());
            documentMapper.updateProcessingStartedAt(document.getId());

            // 推送分段中状态
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENTING, 10, "正在分段处理...");

            // 3. 获取文件下载URL
            String previewUrl = document.getPreviewUrl();
            if (previewUrl == null || previewUrl.trim().isEmpty()) {
                log.error("文档预览URL为空: documentId={}, documentName={}", document.getId(), document.getName());
                documentMapper.updateError(document.getId(), DocumentStatus.DOWNLOAD_FILE_ERROR.getCode(), "文档预览URL为空");

                // 推送错误状态
                documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.DOWNLOAD_FILE_ERROR, 0, "文档预览URL为空");
                return false;
            }

            // 4. 下载文件并进行分段处理
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENTING, 30, "正在下载和解析文件...");
            List<SegmentResult> segmentResults = downloadAndProcessFile(document, previewUrl);
            if (segmentResults.isEmpty()) {
                log.warn("文档分段结果为空: documentId={}, documentName={}", document.getId(), document.getName());
                documentMapper.updateError(document.getId(), DocumentStatus.SEGMENT_ERROR.getCode(), "文档分段结果为空");

                // 推送错误状态
                documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENT_ERROR, 0, "文档分段结果为空");
                return false;
            }

            // 5. 删除已存在的分段
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENTING, 50, "正在清理旧分段数据...");
            documentSegmentMapper.deleteByDocumentId(document.getId());

            // 6. 保存分段结果
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENTING, 70, "正在保存分段结果...");
            List<DocumentSegment> segments = convertToDocumentSegments(document, segmentResults);
            // 获取向量库配置
            Dataset dataset = datasetMapper.selectById(document.getDatasetId());
            Map<String, Object> vectorizationConfig = dataset.getVectorizationConfig();
            KnowledgeVectorizationConfig knowledgeVectorizationConfig = BeanUtil.toBean(vectorizationConfig, KnowledgeVectorizationConfig.class);
            // 获取向量数据库信息
            VectorDatabase vectorDatabase = vectorDatabaseMapper.selectById(dataset.getVectorDatabaseId());
            // 获取向量模型信息
            AiModel aiModel = aiModelMapper.selectById(knowledgeVectorizationConfig.getEmbeddingModelId());

            if (!segments.isEmpty()) {
                List<BatchResult> insertResult = documentSegmentMapper.insert(segments);
                if (!insertResult.isEmpty()) {
                    int insertSize = insertResult.getFirst().getUpdateCounts().length;
                    if (insertSize == segments.size()) {
                        if (rabbitMQProducer != null) {
                            segments.stream().parallel().forEach(segment -> {
                                DocumentSegmentVO segmentVO = new DocumentSegmentVO();
                                BeanUtils.copyProperties(segment, segmentVO);
                                segmentVO.setDocument(document);
                                segmentVO.setKnowledgeVectorizationConfig(knowledgeVectorizationConfig);
                                segmentVO.setVectorDatabase(vectorDatabase);
                                segmentVO.setEmbeddingModel(aiModel);
                                rabbitMQProducer.sendEmbeddingProcessingMessage(segmentVO, segment.getId(), tenantId, userId);
                            });
                        }
                    }
                }
            }

            // 推送分段进度更新
            document.setSegmentCount(segments.size());
            documentMapper.updateSegmentCount(document.getId(), document.getSegmentCount());
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENTING, 80, "正在更新统计信息...");

            // 7. 更新文档统计信息
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENTING, 90, "正在更新统计信息...");
            int totalWordCount = segmentResults.stream().mapToInt(SegmentResult::getWordCount).sum();
            int totalTokens = segmentResults.stream().mapToInt(SegmentResult::getTokens).sum();
            documentMapper.updateProcessingProgress(document.getId(), DocumentStatus.SEGMENTED.getCode(), totalWordCount, totalTokens);

            // 8. 更新文档状态为分段完成
            documentMapper.updateSplittingCompletedAt(document.getId());

            // 推送完成状态
            documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SEGMENTED, 100, "分段处理完成");
            return true;

        } catch (Exception e) {
            log.error("文档分段处理失败: documentId={}, documentName={} error={}", document.getId(), document.getName(), e.getMessage(), e);

            // 关键：设置回滚仅限当前异常，不影响后续操作
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            // 在新的非事务上下文中更新错误信息
            documentMapper.updateError(document.getId(), DocumentStatus.SEGMENT_ERROR.getCode(), "文档分段处理失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<DocumentSegmentVO> listByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId)
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getPosition);
        List<DocumentSegment> segments = list(wrapper);
        return segments.stream().map(segment -> {
            DocumentSegmentVO segmentVO = new DocumentSegmentVO();
            BeanUtils.copyProperties(segment, segmentVO);
            DocumentStatus documentStatus = DocumentStatus.fromCode(segment.getStatus());
            segmentVO.setDocumentStatusDesc(documentStatus.getDescription());
            segmentVO.setDocumentStatusBgColor(documentStatus.getCss());
            return segmentVO;
        }).toList();
    }

    @Override
    public List<DocumentSegment> listByDatasetId(String datasetId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId)
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getPosition);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<DocumentSegment> segments) {
        return saveBatch(segments);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String segmentId, String status, String error) {
        DocumentSegment segment = getById(segmentId);
        if (segment != null) {
            segment.setStatus(status);
            segment.setError(error);
            segment.setUpdateTime(LocalDateTime.now());

            if ("completed".equals(status)) {
                segment.setCompletedAt(LocalDateTime.now());
            } else if ("error".equals(status)) {
                segment.setStoppedAt(LocalDateTime.now());
            }

            return updateById(segment);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<String> segmentIds, String status) {
        if (segmentIds == null || segmentIds.isEmpty()) {
            return 0;
        }

        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DocumentSegment::getId, segmentIds);

        DocumentSegment updateEntity = new DocumentSegment();
        updateEntity.setStatus(status);
        updateEntity.setUpdateTime(LocalDateTime.now());

        return baseMapper.update(updateEntity, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId);
        return baseMapper.delete(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByDatasetId(String datasetId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId);
        return baseMapper.delete(wrapper);
    }

    @Override
    public Long countByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId);
        return count(wrapper);
    }

    @Override
    public Long countByDatasetId(String datasetId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId);
        return count(wrapper);
    }

    @Override
    public Long countVectorizedByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId)
                .eq(DocumentSegment::getStatus, "completed");
        return count(wrapper);
    }

    @Override
    public List<DocumentSegment> getWaitingSegments(Integer limit) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getStatus, "waiting")
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getCreateTime);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        return list(wrapper);
    }

    @Override
    public List<DocumentSegment> getProcessingSegments() {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getStatus, "processing")
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<DocumentSegment> searchSimilarSegments(float[] queryVector, String datasetId,
            int topK, double threshold) {
        // TODO: 实现向量相似度搜索
        // 这里需要集成向量数据库进行相似度搜索
        log.info("搜索相似分段: datasetId={}, topK={}, threshold={}", datasetId, topK, threshold);
        return Collections.emptyList();
    }

    @Override
    public List<DocumentSegment> searchByKeyword(String keyword, String datasetId, int topK) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId)
                .eq(DocumentSegment::getEnabled, true)
                .like(DocumentSegment::getContent, keyword)
                .orderByDesc(DocumentSegment::getHitCount)
                .last("LIMIT " + topK);
        return list(wrapper);
    }

    @Override
    public List<DocumentSegment> hybridSearch(float[] queryVector, String keyword, String datasetId,
            int topK, double vectorWeight, double keywordWeight) {
        // TODO: 实现混合搜索逻辑
        // 这里需要结合向量搜索和关键字搜索的结果
        log.info("混合搜索: datasetId={}, keyword={}, topK={}, vectorWeight={}, keywordWeight={}",
                datasetId, keyword, topK, vectorWeight, keywordWeight);

        // 临时实现：只返回关键字搜索结果
        return searchByKeyword(keyword, datasetId, topK);
    }

    @Override
    public Object getSegmentStats(String documentId) {
        // TODO: 实现分段统计信息
        return Collections.emptyMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resegmentDocument(String documentId, int chunkSize, int chunkOverlap) {
        // TODO: 实现文档重新分段
        log.info("重新分段文档: documentId={}, chunkSize={}, chunkOverlap={}",
                documentId, chunkSize, chunkOverlap);
        return true;
    }

    /**
     * 下载文件并进行分段处理
     *
     * @param document 文档信息
     * @param previewUrl 预览URL
     * @return 分段结果列表
     * @throws Exception 处理异常
     */
    @Override
    public List<SegmentResult> downloadAndProcessFile(Document document, String previewUrl) throws Exception {
        log.info("开始下载文件: documentId={}, url={}", document.getId(), previewUrl);

        try (InputStream inputStream = new URL(previewUrl).openStream()) {
            // 获取文件扩展名
            String fileExtension = document.getDocType();
            if (fileExtension.isEmpty()) {
                throw new Exception("无法确定文件类型");
            }

            // 查找合适的分段处理器
            IFileSegmentationProcessor processor = findProcessor(fileExtension);
            if (processor == null) {
                throw new Exception("不支持的文件类型: " + fileExtension);
            }

            log.info("使用分段处理器: {}, 文件类型: {}", processor.getProcessorName(), fileExtension);

            // 执行分段处理
            return processor.processSegmentation(document, inputStream);
        } catch (Exception e) {
            log.error("下载文件或分段处理失败: documentId={}, url={}, error={}",
                    document.getId(), previewUrl, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean processDocumentSegmentEmbedding(DocumentSegmentVO documentSegmentVO, String tenantId, String userId) {
        try {
            String embeddingModelId = documentSegmentVO.getEmbeddingModel().getId();

            // 更新分段的向量化状态
            documentSegmentMapper.updateStatus(documentSegmentVO.getId(), DocumentStatus.INDEXING.getCode());

            // 根据向量模型的id,对分段的内容进行向量化
            float[] vectors = embeddingService.embedText(embeddingModelId, documentSegmentVO.getContent());
            documentSegmentVO.setVector(vectors);

            VectorDatabase vectorDatabase = documentSegmentVO.getVectorDatabase();
            // 将向量保存到数据库中
            IVectorStoreProcessor vectorStoreProcessor = findVectorStoreProcessor(vectorDatabase.getType());

            Result<String> resultEmbedding = vectorStoreProcessor.storeEmbeddings(documentSegmentVO);
            if (resultEmbedding.getSuccess()){
                Embedding embedding = new Embedding();
                embedding.setId(documentSegmentVO.getId());
                embedding.setModelId(documentSegmentVO.getEmbeddingModel().getId());
                // 成功时，更新分段的向量化状态，向量化的数据
                documentSegmentMapper.updateStatusError(documentSegmentVO.getId(), DocumentStatus.INDEXED.getCode(), "");
                embeddingService.save(embedding, vectors);
            }else {
                // 异常时，只 更新分段的向量化状态
                documentSegmentMapper.updateStatusError(documentSegmentVO.getId(), DocumentStatus.INDEX_ERROR.getCode(), "向量化异常:"+resultEmbedding.getMessage());
                return false;
            }
        }catch (Exception e){
            log.error("向量化分段内容失败: documentId={}, error={}", documentSegmentVO.getId(), e.getMessage(), e);
            documentSegmentMapper.updateStatusError(documentSegmentVO.getId(), DocumentStatus.INDEX_ERROR.getCode(), "向量化异常:"+e.getMessage());
            return false;
        }
        log.info("向量化分段内容完成: documentId={}", documentSegmentVO.getId());
        return true;
    }

    /**
     * 查找合适的分段处理器
     *
     * @param fileExtension 文件扩展名
     * @return 分段处理器
     */
    private IFileSegmentationProcessor findProcessor(String fileExtension) {
        return segmentationProcessors.stream()
                .filter(processor -> processor.supports(fileExtension))
                .min(Comparator.comparingInt(IFileSegmentationProcessor::getPriority))
                .orElse(null);
    }

    /**
     * 查找合适的向量库存储的处理器
     *
     * @param vectorDbType 文件扩展名
     * @return 分段处理器
     */
    private IVectorStoreProcessor findVectorStoreProcessor(String vectorDbType) {
        return vectorStoreProcessors.stream()
                .filter(processor -> processor.supports(vectorDbType))
                .findFirst()
                .orElse(null);
    }

    /**
     * 转换为文档分段实体
     *
     * @param document 文档信息
     * @param segmentResults 分段结果列表
     * @return 文档分段实体列表
     */
    private List<DocumentSegment> convertToDocumentSegments(Document document, List<SegmentResult> segmentResults) {
        List<DocumentSegment> segments = new ArrayList<>();

        for (SegmentResult result : segmentResults) {
            DocumentSegment segment = new DocumentSegment();
            segment.setId( UUID.randomUUID().toString().replace("-", ""));
            segment.setDatasetId(document.getDatasetId());
            segment.setDocumentId(document.getId());
            segment.setPosition(result.getPosition());
            segment.setContent(result.getContent());
            segment.setWordCount(result.getWordCount());
            segment.setTokens(result.getTokens());
            segment.setKeywords(result.getKeywords());
            segment.setHitCount(0);  // 设置命中次数默认值
            segment.setEnabled(true);
            segment.setStatus(DocumentStatus.INDEX_WAITING.getCode());

            // 设置租户信息
            segment.setTenantId(document.getTenantId());
            segment.setCreateBy(document.getCreateBy());
            segment.setCreateTime(LocalDateTime.now());
            segment.setUpdateTime(LocalDateTime.now());
            segment.setDeleted(0);

            segments.add(segment);
        }

        return segments;
    }

}
