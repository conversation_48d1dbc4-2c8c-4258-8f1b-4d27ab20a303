package com.xhcai.modules.rag.service;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import cn.hutool.core.io.FileUtil;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.service.segmentation.impl.MdFileSegmentationProcessor;
import org.slf4j.LoggerFactory;

import java.io.BufferedInputStream;
import java.util.List;

public class MdFileSegmentTest {
    public static void main(String[] args) {
        // ✅ 配置日志级别，让debug日志在控制台输出
        Logger rootLogger = (Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
        rootLogger.setLevel(Level.DEBUG);

        // 也可以只针对特定的类设置debug级别
        Logger logger = (Logger) LoggerFactory.getLogger(MdFileSegmentTest.class);
        logger.setLevel(Level.DEBUG);


        MdFileSegmentationProcessor processor = new MdFileSegmentationProcessor();
        Document document = new Document();
        document.setId("xinghuo-md-001");
        document.setName("实现总结.md");
        document.setDocType("md");

        try {
            BufferedInputStream inputStream = FileUtil.getInputStream("D:\\data\\实现总结.md");
//            String s = processor.extractTextFromMd(document, inputStream);
//            logger.info(s);

            List<SegmentResult> segmentResults = processor.processSegmentationCatalog(document, inputStream);
            segmentResults.forEach(segmentResult -> {logger.info("段落：    " + segmentResult.getContent());});
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
    }
}
