package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 向量查询知识库参数
 */
@Data
public class KnowledgeVectorQueryDTO {
    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    @NotBlank(message = "知识库ID不能为空")
    private String datasetId;

    /**
     * 向量库ID
     */
    @Schema(description = "向量库ID")
    private String vectorDbId;
    /**
     * 向量库模型ID  来源于 models.id
     */
    @Schema(description = "向量库模型ID  来源于 models.id")
    private String vectorModelId;

    /**
     * 进行向量化的内容
     * 不需要进行向量化的文本，后台程序会自动进行向量化
     */
    @Schema(description = "查询内容，不需要进行向量化的文本，后台程序会自动进行向量化")
    private String query;
    @Schema(description = "租户ID")
    private String tenantId;
    @Schema(description = "文档ID")
    private String documentId;
    @Schema(description = "是否可用 1:可用  0：不可用")
    private String enabled;
    @Schema(description = "创建人ID")
    private String createBy;
    @Schema(description = "文档类型")
    private String docType;
    @Schema(description = "文件名称")
    private String name;
    @Schema(description = "分段排序号")
    private Integer position;
    @Schema(description = "关键词")
    private String keyword;

    /**
     * 查询向量返回条数
     */
    @Schema(description = "查询向量返回条数,默认5个")
    @Size(min = 3, max = 200, message = "查询向量返回条数 必须在1-200个字符之间,默认5个")
    private Integer maxResults = 5;
}
