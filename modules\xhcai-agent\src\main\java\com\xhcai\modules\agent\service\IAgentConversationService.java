package com.xhcai.modules.agent.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.agent.dto.AgentConversationQueryDTO;
import com.xhcai.modules.agent.entity.AgentConversation;

/**
 * 智能体对话服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IAgentConversationService extends IService<AgentConversation> {

    /**
     * 根据智能体ID分页查询会话记录，并统计每个会话的消息数
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<AgentConversation> pageByAgentIdWithMessageCount(AgentConversationQueryDTO queryDTO);
}
