package com.xhcai.modules.rag.service;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import cn.hutool.core.io.FileUtil;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.service.segmentation.impl.PdfFileSegmentationProcessor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.slf4j.LoggerFactory;

import java.io.BufferedInputStream;

public class PdfSplitSegemntTest {
    public static void main(String[] args) {
        // ✅ 配置日志级别，让debug日志在控制台输出
        Logger rootLogger = (Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
        rootLogger.setLevel(Level.DEBUG);

        // 也可以只针对特定的类设置debug级别
        Logger pdfProcessorLogger = (Logger) LoggerFactory.getLogger(PdfFileSegmentationProcessor.class);
        pdfProcessorLogger.setLevel(Level.DEBUG);

        System.out.println("=== PDF文档按分隔符测试 ===");
        System.out.println("已启用DEBUG日志输出");

        PdfFileSegmentationProcessor processor = new PdfFileSegmentationProcessor();
        Document document = new Document();
        document.setId("xinghuo-pdf-001");
        document.setName("xinghuo.pdf");

        try (BufferedInputStream inputStream = FileUtil.getInputStream("D:\\data\\xinghuo.pdf");
             PDDocument pdfDocument = PDDocument.load(inputStream)){
//            List<SegmentResult> segmentResults = processor.processSegmentation(document, inputStream);
//            for (SegmentResult segmentResult : segmentResults) {
//                System.out.println(segmentResult);
//            }
            // 提取PDF文本内容
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");

            String text = stripper.getText(pdfDocument);
            text = cleanText(text);
            System.out.println( text);
        }catch (Exception e){

        }
    }

    public static String cleanText(String text) {
        if (text == null) {
            return "";
        }

//        // 移除多余的空白字符
//        text = text.replaceAll("\\s+", " ");
        text = text.replaceAll("[ \\t\\f]+", " ");
        // 移除特殊字符（保留基本标点）
        text = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        return text.trim();
    }
}
