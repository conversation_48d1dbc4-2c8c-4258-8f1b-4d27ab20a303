package com.xhcai.modules.rag.service;

import cn.hutool.core.io.FileUtil;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.service.segmentation.impl.WordFileSegmentationProcessor;

import java.io.BufferedInputStream;
import java.util.List;

public class WordFileSegmentationTest {
//    private static final int DEFAULT_CHUNK_SIZE = 1000;
//    private static final int DEFAULT_OVERLAP_SIZE = 0;

    public static void main(String[] args) {
        WordFileSegmentationProcessor processor = new WordFileSegmentationProcessor();
        Document document = new Document();
        document.setId("test-doc-001");
        document.setName("机动车登记规定.docx");

        try {
            System.out.println("使用测试文件: D:\\data\\机动车登记规定.docx");
            System.out.println("测试增强的目录段落检查功能（包含TOC段落检查）...");

            BufferedInputStream inputStream = FileUtil.getInputStream("D:\\data\\机动车登记规定.docx");

            List<SegmentResult> results = processor.processSegmentationCatalog(document, inputStream);

            System.out.println("\n=== 分段配置信息 ===");

            System.out.println("共生成分段数量: " + results.size());

            // 统计分段大小分布
            int oversizeCount = 0;
            int maxSize = 0;
            int minSize = Integer.MAX_VALUE;
            int DEFAULT_CHUNK_SIZE = 1024;
            for (int i = 0; i < results.size(); i++) {
                SegmentResult result = results.get(i);
                int size = result.getWordCount();

                if (size > DEFAULT_CHUNK_SIZE) {
                    oversizeCount++;
                }
                maxSize = Math.max(maxSize, size);
                minSize = Math.min(minSize, size);

                System.out.println("\n--- 分段 " + (i + 1) + " ---");
                System.out.println("位置: " + result.getPosition());
                System.out.println("字符数: " + result.getWordCount() +
                    (size > DEFAULT_CHUNK_SIZE ? " ⚠️超出限制" : " ✅符合限制"));
                System.out.println("关键词: " + result.getKeywords());
//                System.out.println("内容预览: " +
//                    (result.getContent().length() > 150 ?
//                        result.getContent().substring(0, 150) + "..." :
//                        result.getContent()));
                System.out.println("内容预览: " + result.getContent());
            }

            System.out.println("\n=== 分段统计 ===");
            System.out.println("最大分段大小: " + maxSize + " 字符");
            System.out.println("最小分段大小: " + (results.isEmpty() ? 0 : minSize) + " 字符");
            System.out.println("超出大小限制的分段数: " + oversizeCount);
            if (!results.isEmpty()) {
                System.out.println("分段大小合规率: " + String.format("%.1f%%",
                    (results.size() - oversizeCount) * 100.0 / results.size()));
            }

            // 验证内容完整性
            System.out.println("\n=== 内容完整性验证 ===");
            int totalSegmentLength = results.stream().mapToInt(r -> r.getContent().length()).sum();
            System.out.println("分段总长度: " + totalSegmentLength + " 字符");

            // 验证机动车登记规定的关键内容
            String[] keyPhrases = {"机动车", "登记", "申请", "办理", "证明", "管理所", "第一条", "第二条", "第三条"};
            int foundInSegments = 0;
            for (String phrase : keyPhrases) {
                boolean found = results.stream().anyMatch(r -> r.getContent().contains(phrase));
                if (found) {
                    foundInSegments++;
                    System.out.println("\"" + phrase + "\": ✅存在");
                } else {
                    System.out.println("\"" + phrase + "\": ❌不存在");
                }
            }
            System.out.println("关键短语保留: " + foundInSegments + "/" + keyPhrases.length);

            // ✅ 专门验证目录段落检查功能（包含TOC段落检查）
            System.out.println("\n=== 目录段落检查验证 ===");
            int titleSegments = 0;
            int articleSegments = 0; // 条文分段
            int chapterSegments = 0; // 章节分段
            int tocSegments = 0; // TOC目录分段

            for (SegmentResult result : results) {
                String content = result.getContent();

                // 检查是否包含标题标记
                if (content.contains("【") && content.contains("级标题】")) {
                    titleSegments++;

                    // 分析标题类型
                    if (content.contains("第") && content.contains("条")) {
                        articleSegments++;
                        System.out.println("发现条文分段: " + extractTitleFromContent(content));
                    } else if (content.contains("第") && (content.contains("章") || content.contains("节"))) {
                        chapterSegments++;
                        System.out.println("发现章节分段: " + extractTitleFromContent(content));
                    } else if (content.contains("TOC") || content.contains("目录")) {
                        tocSegments++;
                        System.out.println("发现TOC目录分段: " + extractTitleFromContent(content));
                    } else {
                        System.out.println("发现其他标题分段: " + extractTitleFromContent(content));
                    }
                }
            }

            System.out.println("标题分段总数: " + titleSegments);
            System.out.println("条文分段数: " + articleSegments);
            System.out.println("章节分段数: " + chapterSegments);
            System.out.println("TOC目录分段数: " + tocSegments);

            // 验证分段是否遵守了大小限制
            System.out.println("\n=== 分段质量验证 ===");
            boolean sizeCompliant = oversizeCount == 0;
            boolean contentComplete = foundInSegments >= keyPhrases.length * 0.8;

            System.out.println("✅ 大小限制遵守: " + (sizeCompliant ? "通过" : "失败"));
            System.out.println("✅ 内容完整性: " + (contentComplete ? "通过" : "失败"));

            // 额外检查：验证是否有分段超出限制
            if (oversizeCount > 0) {
                System.out.println("\n⚠️ 发现分段大小问题:");
                for (int i = 0; i < results.size(); i++) {
                    SegmentResult result = results.get(i);
                    if (result.getContent().length() > DEFAULT_CHUNK_SIZE) {
                        System.out.println("  分段 " + (i + 1) + ": " + result.getContent().length() +
                            " 字符 (超出 " + (result.getContent().length() - DEFAULT_CHUNK_SIZE) + " 字符)");
                    }
                }
                System.out.println("  原因: 剩余内容被合并到最后分段时未检查大小限制");
                System.out.println("  解决: 已修复合并逻辑，超出限制时会创建新分段");
            }

            if (sizeCompliant && contentComplete) {
                System.out.println("\n🎉 所有测试通过！修复成功，内容完整性和分段大小限制都得到保证！");
            } else {
                System.out.println("\n⚠️ 测试发现问题，需要进一步检查");
                if (!sizeCompliant) {
                    System.out.println("  - 分段大小限制问题已修复");
                }
                if (!contentComplete) {
                    System.out.println("  - 内容完整性问题需要进一步调查");
                }
            }

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 从分段内容中提取标题文本
     * @param content 分段内容
     * @return 标题文本
     */
    private static String extractTitleFromContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // 查找标题标记
        int startIndex = content.indexOf("【");
        int endIndex = content.indexOf("】");

        if (startIndex >= 0 && endIndex > startIndex) {
            // 提取标题标记后的内容
            String afterTitle = content.substring(endIndex + 1).trim();
            String[] lines = afterTitle.split("\n");
            if (lines.length > 0) {
                String titleLine = lines[0].trim();
                return titleLine.length() > 50 ? titleLine.substring(0, 50) + "..." : titleLine;
            }
        }

        // 如果没有找到标题标记，返回前50个字符
        String firstLine = content.split("\n")[0].trim();
        return firstLine.length() > 50 ? firstLine.substring(0, 50) + "..." : firstLine;
    }
}
