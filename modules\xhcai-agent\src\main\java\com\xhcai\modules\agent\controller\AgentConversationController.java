package com.xhcai.modules.agent.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.agent.dto.AgentConversationQueryDTO;
import com.xhcai.modules.agent.entity.AgentConversation;
import com.xhcai.modules.agent.service.IAgentConversationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能体会话记录
 *
 * 根据智能体ID分页查询会话记录（表 agent_conversation）
 */
@Tag(name = "智能体会话记录", description = "智能体会话记录的查询、管理等功能")
@RestController
@RequestMapping("/api/agent/conversations")
public class AgentConversationController {

    @Autowired
    private IAgentConversationService conversationService;

    @Operation(summary = "根据智能体ID分页查询会话记录", description = "参数包含：智能体ID、页码(默认1)、每页数量(默认10)、开始时间、结束时间、排序字段(默认create_time，可选：create_time、update_time、last_activity_at、started_at、ended_at)、排序方向(默认desc)、关键字keyword(匹配title)")
    @GetMapping("/page")
    @RequiresPermissions("conversation:list")
    public Result<PageResult<AgentConversation>> pageByAgentId(@Valid AgentConversationQueryDTO queryDTO) {
        // 使用Service方法，自动统计消息数
        Page<AgentConversation> result = conversationService.pageByAgentIdWithMessageCount(queryDTO);

        // 封装返回
        PageResult<AgentConversation> pageResult = PageResult.of(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
        return Result.success(pageResult);
    }
}
