package com.xhcai.common.security.service.impl;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.JwtUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.common.security.service.ApiKeyAuthenticationService;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import javax.crypto.SecretKey;

/**
 * API密钥认证服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class ApiKeyAuthenticationServiceImpl implements ApiKeyAuthenticationService {

    private static final Logger logger = LoggerFactory.getLogger(ApiKeyAuthenticationServiceImpl.class);

    @Autowired
    private JwtUtils jwtUtils;

    @Override
    public String generateApiKey(String userId, String username, String tenantId, String deptId,
                               String targetType, String targetId, LocalDateTime expiresAt) {
        try {
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", userId);
            claims.put("username", username);
            claims.put("tenantId", tenantId);
            claims.put("deptId", deptId);
            claims.put("targetType", targetType);
            claims.put("targetId", targetId);

            ZoneId zone = ZoneId.systemDefault();

            return Jwts.builder()
                    .claims(claims)
                    .subject(userId)
                    .issuedAt(new Date())
                    .expiration(Date.from(expiresAt.atZone(zone).toInstant()))
                    .signWith(jwtUtils.getSigningKey(), Jwts.SIG.HS256)
                    .compact();

        } catch (Exception e) {
            logger.error("生成API密钥失败", e);
            throw new BusinessException("生成API密钥失败: " + e.getMessage());
        }
    }

    @Override
    public String compositeApiKey(String userId, String username, String tenantId, String deptId,
                               String targetType, String targetId) {
        String apiKey = generateApiKey(userId, username, tenantId, deptId, targetType, targetId,
                LocalDateTime.now().plusDays(30));

        return Base64.getEncoder().encodeToString((apiKey + "." + (new Date()).getTime()).getBytes());
    }

    @Override
    public String generateKeyHash(String apiKey) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(apiKey.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            logger.error("生成密钥哈希失败", e);
            throw new BusinessException("生成密钥哈希失败: " + e.getMessage());
        }
    }
}