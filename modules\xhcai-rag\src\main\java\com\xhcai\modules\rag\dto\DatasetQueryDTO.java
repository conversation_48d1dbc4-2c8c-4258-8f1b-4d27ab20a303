package com.xhcai.modules.rag.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 知识库查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "知识库查询DTO")
public class DatasetQueryDTO extends PageTimeRangeQueryDTO {

    @Schema(description = "知识库名称", example = "技术文档库")
    private String name;

    @Schema(description = "数据源类型", example = "document")
    private String dataSourceType;

    @Schema(description = "模型ID", example = "model_123")
    private String modelId;

    @Schema(description = "部门ID", example = "1111111")
    private String deptId;

    @Schema(description = "标签", example = "多个标签以,隔开")
    private String tags;

    @Schema(description = "租户ID", example = "tenant_123")
    private String tenantId;

    @Schema(description = "创建人ID", example = "user_123")
    private String createBy;

    /**
     * 知识库图标
     */
    @Schema(description = "知识库图标", example = "📚")
    private String icon;

    /**
     * 知识库图标背景色
     */
    @Schema(description = "知识库图标背景色", example = "#3b82f6")
    private String iconBg;

    // ==================== Getters and Setters ====================

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDataSourceType() {
        return dataSourceType;
    }

    public void setDataSourceType(String dataSourceType) {
        this.dataSourceType = dataSourceType;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBg() {
        return iconBg;
    }

    public void setIconBg(String iconBg) {
        this.iconBg = iconBg;
    }

    @Override
    public String toString() {
        return "DatasetQueryDTO{" +
                "name='" + name + '\'' +
                ", dataSourceType='" + dataSourceType + '\'' +
                ", modelId='" + modelId + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", createBy='" + createBy + '\'' +
                ", current=" + getCurrent() + '\'' +
                ", size=" + getSize() + '\'' +
                ", beginTime=" + getBeginTime() + '\'' +
                ", endTime=" + getEndTime() + '\'' +
                ", icon=" + getIcon() + '\'' +
                ", iconBg=" + getIconBg() +
                '}';
    }
}
