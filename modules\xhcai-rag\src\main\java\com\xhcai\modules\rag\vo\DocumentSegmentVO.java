package com.xhcai.modules.rag.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xhcai.modules.rag.entity.*;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.handler.DocumentHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "文档分段VO")
public class DocumentSegmentVO extends DocumentSegment {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文档")
    @TableField(value = "document", typeHandler = DocumentHandler.class, jdbcType = JdbcType.OTHER)
    private Document document;

    @Schema(description = "向量库信息", example = "docSegment123")
    private VectorDatabase vectorDatabase;

    @Schema(description = "索引节点信息", example = "node123")
    private KnowledgeVectorizationConfig knowledgeVectorizationConfig;

    @Schema(description = "嵌入模型信息")
    private AiModel embeddingModel;

    @Schema(description = "文档处理状态描述", example = "已完成")
    private String documentStatusDesc;

    @Schema(description = "文档处理状态背底色", example = "#000000")
    private String documentStatusBgColor;

    /**
     * 向量数据
     */
    @Schema(description = "向量数据", hidden = true)
    private float[] vector;
}
