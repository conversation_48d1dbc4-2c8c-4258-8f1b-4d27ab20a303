package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 批次信息DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "批次信息DTO")
@Data
public class BatchInfoDTO {

    /**
     * 批次号
     */
    @Schema(description = "批次号", example = "batch_20240101_001")
    private String batchId;

    /**
     * 文件数量
     */
    @Schema(description = "文件数量", example = "15")
    private Long fileCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
    private String createTime;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间", example = "2024-01-01 15:30:00")
    private String updateTime;
}
