package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Dify 会话列表响应 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify 会话列表响应")
public class DifyConversationListResponseDTO {

    @Schema(description = "页码")
    @JsonProperty("page")
    private Integer page;

    @Schema(description = "每页限制数量")
    @JsonProperty("limit")
    private Integer limit;

    @Schema(description = "会话列表")
    @JsonProperty("data")
    private List<DifyConversationDTO> data;

    @Schema(description = "是否有更多数据")
    @JsonProperty("has_more")
    private Boolean hasMore;

    @Schema(description = "总数量")
    @JsonProperty("total")
    private Integer total;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public List<DifyConversationDTO> getData() {
        return data;
    }

    public void setData(List<DifyConversationDTO> data) {
        this.data = data;
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "DifyConversationListResponseDTO{" +
                "page=" + page +
                ", limit=" + limit +
                ", data=" + data +
                ", hasMore=" + hasMore +
                ", total=" + total +
                '}';
    }
}
