package com.xhcai.modules.rag.service.segmentation.impl;

import cn.hutool.core.util.StrUtil;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import com.xhcai.modules.rag.service.segmentation.AbstractFileSegmentationProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Md文件分段处理器
 * 1: 非按目录结构公段，按通用规则
 * 2: 按目录结构分段
 *    2.1 如果存在兄弟目录，则每个兄弟目录的子目录为一段
 *    2.2 如果不存在兄弟目录，以子目录为一段
 *    2.3 如果目录级别相同，但存在标题，则将标题为合为一段
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class MdFileSegmentationProcessor extends AbstractFileSegmentationProcessor {

    @Override
    public List<String> getSupportedFileTypes() {
        return Arrays.asList("md", "markdown");
    }

    @Override
    public String getProcessorName() {
        return "md文件分段处理器";
    }

    @Override
    public int getPriority() {
        return 50;
    }

    @Override
    public List<SegmentResult> processSegmentation(Document document, InputStream inputStream) throws Exception {
        log.info("开始处理md文件分段: documentId={}, fileName={}", document.getId(), document.getName());

        try {
            SegmentConfig segmentConfig = document.getSegmentConfig();
            String type = segmentConfig.getType();

            List<SegmentResult> segments = null;
            String text = null;
            switch (type) {
                case "directory":
                    // 按目录结构分段
                    segments = processSegmentationCatalog(document, inputStream);
                    break;
                case "constantLength":
                    // 按固定长度分段
                    text = extractTextFromMd(document, inputStream);
                    segments = segmentByFixedSize(text, segmentConfig.getConstantLength());
                    break;
                case "natural":
                    // 按自然段分段
                    text = extractTextFromMd(document, inputStream);
                    segments = segmentByParagraphs(text, segmentConfig.getNatural());
                    break;
                case "delimiter":
                    // 按分隔符分段
                    text = extractTextFromMd(document, inputStream);
                    segments = segmentByDelimiter(text, segmentConfig.getDelimiter());
                    break;
                case "none":
                    // 不分段
                    text = extractTextFromMd(document, inputStream);
                    segments = List.of(SegmentResult.create(text, 1, extractKeywords(text)));
                    break;
                default:
                    log.warn("未知的分段类型: {}, 使用默认的目录分段", type);
                    segments = processSegmentationCatalog(document, inputStream);
                    break;
            }

            log.info("Md文件分段完成: documentId={}, 分段数量={}", document.getId(), segments != null ? segments.size() : 0);
            return segments != null ? segments : new ArrayList<>();

        } catch (Exception e) {
            log.error("Md文档分段处理失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            throw new Exception("Md文档分段处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从md文档中提取文本
     *
     * @param document    文档信息
     * @param inputStream 输入流
     * @return 提取的纯文本内容
     * @throws Exception 处理异常
     */
    public String extractTextFromMd(Document document, InputStream inputStream) throws Exception {
        log.info("开始从md文档中提取文本: documentId={}, fileName={}", document.getId(), document.getName());

        try {
            StringBuilder content = new StringBuilder();
            CleaningConfig cleaningConfig = document.getCleaningConfig();

            // 读取文件内容
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 处理Markdown语法，提取纯文本
                    String cleanedLine = extractTextFromMarkdownLine(line);
                    if (StrUtil.isNotBlank(cleanedLine)) {
                        // 应用清洗配置
                        cleanedLine = cleanText(cleanedLine, cleaningConfig);
                        if (StrUtil.isNotBlank(cleanedLine)) {
                            content.append(cleanedLine).append(StrUtil.LF);
                        }
                    }
                }
            }

            String result = content.toString().trim();
            log.info("md文档文本提取完成: documentId={}, 提取文本长度={}", document.getId(), result.length());
            return result;

        } catch (Exception e) {
            log.error("md文档文本提取失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            throw new Exception("md文档文本提取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从Markdown行中提取纯文本
     *
     * @param line Markdown行
     * @return 纯文本
     */
    private String extractTextFromMarkdownLine(String line) {
        if (line == null) {
            return "";
        }

        // 去除前后空白
        line = line.trim();

        // 跳过空行
        if (line.isEmpty()) {
            return "";
        }

        // 处理代码块标记
        if (line.startsWith("```") || line.startsWith("~~~")) {
            return ""; // 跳过代码块分隔符
        }

        // 处理标题 (# ## ### #### ##### ######)
        if (line.startsWith("#")) {
            return line.replaceFirst("^#+\\s*", "").trim();
        }

        // 处理引用 (>)
        if (line.startsWith(">")) {
            return line.replaceFirst("^>+\\s*", "").trim();
        }

        // 处理列表项 (- * + 1. 2. etc.)
        if (line.matches("^\\s*[-*+]\\s+.*") || line.matches("^\\s*\\d+\\.\\s+.*")) {
            return line.replaceFirst("^\\s*[-*+\\d.]+\\s*", "").trim();
        }

        // 处理表格分隔符
        if (line.matches("^\\s*\\|?\\s*:?-+:?\\s*(\\|\\s*:?-+:?\\s*)*\\|?\\s*$")) {
            return ""; // 跳过表格分隔符行
        }

        // 处理表格行
        if (line.contains("|")) {
            // 移除表格边框，保留内容
            line = line.replaceAll("^\\s*\\|\\s*", "").replaceAll("\\s*\\|\\s*$", "");
            line = line.replaceAll("\\s*\\|\\s*", " ");
        }

        // 处理内联格式
        line = removeInlineMarkdown(line);

        return line.trim();
    }

    /**
     * 移除内联Markdown格式
     *
     * @param text 文本
     * @return 清理后的文本
     */
    private String removeInlineMarkdown(String text) {
        if (text == null) {
            return "";
        }

        // 移除粗体 **text** 或 __text__
        text = text.replaceAll("\\*\\*(.*?)\\*\\*", "$1");
        text = text.replaceAll("__(.*?)__", "$1");

        // 移除斜体 *text* 或 _text_
        text = text.replaceAll("\\*(.*?)\\*", "$1");
        text = text.replaceAll("_(.*?)_", "$1");

        // 移除删除线 ~~text~~
        text = text.replaceAll("~~(.*?)~~", "$1");

        // 移除内联代码 `code`
        text = text.replaceAll("`([^`]*)`", "$1");

        // 移除链接 [text](url) 保留text
        text = text.replaceAll("\\[([^\\]]+)\\]\\([^)]+\\)", "$1");

        // 移除图片 ![alt](url)
        text = text.replaceAll("!\\[[^\\]]*\\]\\([^)]+\\)", "");

        // 移除HTML标签
        text = text.replaceAll("<[^>]+>", "");

        return text;
    }

    /**
     * 按目录读取文件并进行分段
     *
     * @param document    文档信息
     * @param inputStream 输入流
     * @return 分段结果列表
     */
    public List<SegmentResult> processSegmentationCatalog(Document document, InputStream inputStream) {
        log.info("开始按目录结构分段md文档: documentId={}, fileName={}", document.getId(), document.getName());

        try {
            // 解析Markdown文档的目录结构
            List<MarkdownSection> sections = parseMarkdownSections(inputStream, document.getCleaningConfig());

            // 根据目录结构进行分段
            List<SegmentResult> segments = segmentBySections(sections);

            log.info("md文档目录分段完成: documentId={}, 分段数量={}", document.getId(), segments.size());
            return segments;

        } catch (Exception e) {
            log.error("md文档目录分段失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析Markdown文档的章节结构
     *
     * @param inputStream    输入流
     * @param cleaningConfig 清洗配置
     * @return 章节列表
     * @throws Exception 处理异常
     */
    private List<MarkdownSection> parseMarkdownSections(InputStream inputStream, CleaningConfig cleaningConfig) throws Exception {
        List<MarkdownSection> sections = new ArrayList<>();
        List<String> lines = new ArrayList<>();

        // 读取所有行
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }
        }

        MarkdownSection currentSection = null;
        boolean inCodeBlock = false;

        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);

            // 检查代码块状态
            if (line.trim().startsWith("```") || line.trim().startsWith("~~~")) {
                inCodeBlock = !inCodeBlock;
                if (currentSection != null) {
                    currentSection.addContent(line);
                }
                continue;
            }

            // 在代码块内，直接添加内容
            if (inCodeBlock) {
                if (currentSection != null) {
                    currentSection.addContent(line);
                }
                continue;
            }

            // 检查是否为标题行
            int headingLevel = getHeadingLevel(line);
            if (headingLevel > 0) {
                // 保存当前章节
                if (currentSection != null && currentSection.hasContent()) {
                    sections.add(currentSection);
                }

                // 创建新章节
                String title = extractHeadingText(line);
                currentSection = new MarkdownSection(title, headingLevel, i);
//                log.debug("发现标题: level={}, title={}, line={}", headingLevel, title, i);
            } else {
                // 普通内容行
                if (currentSection == null) {
                    // 如果还没有章节，创建一个默认章节
                    currentSection = new MarkdownSection("文档开头", 1, 0);
                }

                // 提取纯文本并添加到当前章节
                String cleanedLine = extractTextFromMarkdownLine(line);
                if (StrUtil.isNotBlank(cleanedLine)) {
                    cleanedLine = cleanText(cleanedLine, cleaningConfig);
                    if (StrUtil.isNotBlank(cleanedLine)) {
                        currentSection.addContent(cleanedLine);
                    }
                }
            }
        }

        // 添加最后一个章节
        if (currentSection != null && currentSection.hasContent()) {
            sections.add(currentSection);
        }

        return sections;
    }

    /**
     * 获取标题级别
     *
     * @param line 行内容
     * @return 标题级别，0表示非标题
     */
    private int getHeadingLevel(String line) {
        if (line == null || line.trim().isEmpty()) {
            return 0;
        }

        line = line.trim();
        if (line.startsWith("#")) {
            int level = 0;
            for (char c : line.toCharArray()) {
                if (c == '#') {
                    level++;
                } else if (c == ' ') {
                    break;
                } else {
                    return 0; // 不是有效的标题格式
                }
            }
            return Math.min(level, 6); // 最多6级标题
        }

        return 0;
    }

    /**
     * 提取标题文本
     *
     * @param line 标题行
     * @return 标题文本
     */
    private String extractHeadingText(String line) {
        if (line == null) {
            return "";
        }

        return line.replaceFirst("^#+\\s*", "").trim();
    }

    /**
     * 根据章节结构进行分段
     *
     * @param sections 章节列表
     * @return 分段结果列表
     */
    private List<SegmentResult> segmentBySections(List<MarkdownSection> sections) {
        List<SegmentResult> segments = new ArrayList<>();

        if (sections.isEmpty()) {
            return segments;
        }

        // 按照分段规则处理章节
        List<MarkdownSection> processedSections = applySectionGroupingRules(sections);

        // 将处理后的章节转换为分段结果
        for (int i = 0; i < processedSections.size(); i++) {
            MarkdownSection section = processedSections.get(i);
            String content = section.getContent().toString().trim();

            if (StrUtil.isNotBlank(content)) {
                List<String> keywords = extractKeywords(content);
                keywords.add(section.getTitle()); // 添加标题作为关键词

                segments.add(SegmentResult.create(content, i + 1, keywords));
            }
        }

        return segments;
    }

    /**
     * 应用章节分组规则
     *
     * @param sections 原始章节列表
     * @return 处理后的章节列表
     */
    private List<MarkdownSection> applySectionGroupingRules(List<MarkdownSection> sections) {
        List<MarkdownSection> result = new ArrayList<>();

        for (int i = 0; i < sections.size(); i++) {
            MarkdownSection currentSection = sections.get(i);

            // 查找兄弟章节和子章节
            List<MarkdownSection> siblings = findSiblings(sections, i);
            List<MarkdownSection> children = findChildren(sections, i);

//            log.debug("处理章节: title={}, level={}, siblings={}, children={}",
//                    currentSection.getTitle(), currentSection.getLevel(), siblings.size(), children.size());

            if (!siblings.isEmpty()) {
                // 规则2.1: 如果存在兄弟目录，则每个兄弟目录的子目录为一段
                MarkdownSection groupedSection = groupSectionWithChildren(currentSection, children);
                result.add(groupedSection);

                // 跳过已处理的子章节
                i += children.size();
            } else if (!children.isEmpty()) {
                // 规则2.2: 如果不存在兄弟目录，以子目录为一段
                result.add(currentSection); // 添加父章节

                // 每个子章节作为独立分段
                for (MarkdownSection child : children) {
                    result.add(child);
                }

                // 跳过已处理的子章节
                i += children.size();
            } else {
                // 规则2.3: 如果目录级别相同，但存在标题，则将标题合为一段
                List<MarkdownSection> sameLevelSections = findSameLevelSections(sections, i);
                if (!sameLevelSections.isEmpty()) {
                    MarkdownSection mergedSection = mergeSameLevelSections(currentSection, sameLevelSections);
                    result.add(mergedSection);

                    // 跳过已处理的同级章节
                    i += sameLevelSections.size();
                } else {
                    // 独立章节
                    result.add(currentSection);
                }
            }
        }

        return result;
    }

    /**
     * 查找兄弟章节（同级别的章节）
     *
     * @param sections     所有章节
     * @param currentIndex 当前章节索引
     * @return 兄弟章节列表
     */
    private List<MarkdownSection> findSiblings(List<MarkdownSection> sections, int currentIndex) {
        List<MarkdownSection> siblings = new ArrayList<>();
        MarkdownSection current = sections.get(currentIndex);

        // 向后查找同级章节
        for (int i = currentIndex + 1; i < sections.size(); i++) {
            MarkdownSection section = sections.get(i);
            if (section.getLevel() == current.getLevel()) {
                siblings.add(section);
            } else if (section.getLevel() < current.getLevel()) {
                // 遇到更高级别的章节，停止查找
                break;
            }
        }

        return siblings;
    }

    /**
     * 查找子章节
     *
     * @param sections     所有章节
     * @param currentIndex 当前章节索引
     * @return 子章节列表
     */
    private List<MarkdownSection> findChildren(List<MarkdownSection> sections, int currentIndex) {
        List<MarkdownSection> children = new ArrayList<>();
        MarkdownSection current = sections.get(currentIndex);

        // 向后查找子章节
        for (int i = currentIndex + 1; i < sections.size(); i++) {
            MarkdownSection section = sections.get(i);
            if (section.getLevel() == current.getLevel() + 1) {
                children.add(section);
            } else if (section.getLevel() <= current.getLevel()) {
                // 遇到同级或更高级别的章节，停止查找
                break;
            }
        }

        return children;
    }

    /**
     * 查找同级别章节
     *
     * @param sections     所有章节
     * @param currentIndex 当前章节索引
     * @return 同级别章节列表
     */
    private List<MarkdownSection> findSameLevelSections(List<MarkdownSection> sections, int currentIndex) {
        List<MarkdownSection> sameLevelSections = new ArrayList<>();
        MarkdownSection current = sections.get(currentIndex);

        // 向后查找连续的同级章节
        for (int i = currentIndex + 1; i < sections.size(); i++) {
            MarkdownSection section = sections.get(i);
            if (section.getLevel() == current.getLevel()) {
                sameLevelSections.add(section);
            } else {
                // 遇到不同级别的章节，停止查找
                break;
            }
        }

        return sameLevelSections;
    }

    /**
     * 将章节与其子章节合并
     *
     * @param parent   父章节
     * @param children 子章节列表
     * @return 合并后的章节
     */
    private MarkdownSection groupSectionWithChildren(MarkdownSection parent, List<MarkdownSection> children) {
        MarkdownSection grouped = new MarkdownSection(parent.getTitle(), parent.getLevel(), parent.getStartIndex());

        // 添加父章节内容
        grouped.getContent().append(parent.getContent());

        // 添加子章节内容
        for (MarkdownSection child : children) {
            if (!grouped.getContent().isEmpty()) {
                grouped.getContent().append("\n\n");
            }
            grouped.getContent().append("## ").append(child.getTitle()).append("\n");
            grouped.getContent().append(child.getContent());
        }

        return grouped;
    }

    /**
     * 合并同级别章节
     *
     * @param first  第一个章节
     * @param others 其他同级章节
     * @return 合并后的章节
     */
    private MarkdownSection mergeSameLevelSections(MarkdownSection first, List<MarkdownSection> others) {
        MarkdownSection merged = new MarkdownSection(first.getTitle(), first.getLevel(), first.getStartIndex());

        // 添加第一个章节内容
        merged.getContent().append(first.getContent());

        // 添加其他章节内容
        for (MarkdownSection section : others) {
            if (merged.getContent().length() > 0) {
                merged.getContent().append("\n\n");
            }
            merged.getContent().append("## ").append(section.getTitle()).append("\n");
            merged.getContent().append(section.getContent());
        }

        return merged;
    }

    /**
     * Markdown章节内部类
     */
    private static class MarkdownSection {
        private String title;           // 章节标题
        private int level;              // 标题级别 (1-6)
        private int startIndex;         // 在文档中的起始行号
        private StringBuilder content;   // 章节内容

        public MarkdownSection(String title, int level, int startIndex) {
            this.title = title;
            this.level = level;
            this.startIndex = startIndex;
            this.content = new StringBuilder();
        }

        public String getTitle() {
            return title;
        }

        public int getLevel() {
            return level;
        }

        public int getStartIndex() {
            return startIndex;
        }

        public StringBuilder getContent() {
            return content;
        }

        public void addContent(String line) {
            if (content.length() > 0) {
                content.append("\n");
            }
            content.append(line);
        }

        public boolean hasContent() {
            return content.length() > 0 && StrUtil.isNotBlank(content.toString());
        }

        @Override
        public String toString() {
            return "MarkdownSection{" +
                    "title='" + title + '\'' +
                    ", level=" + level +
                    ", startIndex=" + startIndex +
                    ", contentLength=" + content.length() +
                    '}';
        }
    }
}
