package com.xhcai.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.agent.dto.AgentConversationQueryDTO;
import com.xhcai.modules.agent.entity.AgentConversation;
import com.xhcai.modules.agent.mapper.AgentConversationMapper;
import com.xhcai.modules.agent.mapper.AgentMessageMapper;
import com.xhcai.modules.agent.service.IAgentConversationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 智能体对话服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AgentConversationServiceImpl extends ServiceImpl<AgentConversationMapper, AgentConversation> implements IAgentConversationService {

    @Autowired
    private AgentMessageMapper agentMessageMapper;

    @Override
    public Page<AgentConversation> pageByAgentIdWithMessageCount(AgentConversationQueryDTO queryDTO) {
        // 分页参数
        Page<AgentConversation> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

        // 组装查询条件
        QueryWrapper<AgentConversation> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_id", queryDTO.getAgentId());

        // 时间范围（按 create_time 过滤）
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.hasText(queryDTO.getBeginTime())) {
            try {
                LocalDateTime begin = LocalDateTime.parse(queryDTO.getBeginTime(), fmt);
                wrapper.ge("create_time", begin);
            } catch (Exception ignored) { }
        }
        if (StringUtils.hasText(queryDTO.getEndTime())) {
            try {
                LocalDateTime end = LocalDateTime.parse(queryDTO.getEndTime(), fmt);
                wrapper.le("create_time", end);
            } catch (Exception ignored) { }
        }

        // 关键词（按标题模糊匹配）
        if (StringUtils.hasText(queryDTO.getKeyword())) {
            wrapper.like("title", queryDTO.getKeyword());
        }

        // 排序字段白名单
        String orderBy = StringUtils.hasText(queryDTO.getOrderBy()) ? queryDTO.getOrderBy() : "create_time";
        switch (orderBy) {
            case "last_activity_at":
            case "started_at":
            case "ended_at":
            case "create_time":
            case "update_time":
                break;
            default:
                orderBy = "create_time";
        }
        boolean isAsc = "asc".equalsIgnoreCase(queryDTO.getOrderDirection());
        wrapper.orderBy(true, isAsc, orderBy);

        // 执行查询
        Page<AgentConversation> result = baseMapper.selectPage(page, wrapper);

        // 为每个对话统计消息数
        List<AgentConversation> records = result.getRecords();
        String tenantId = SecurityUtils.getCurrentTenantId();
        for (AgentConversation conversation : records) {
            // 使用专门的方法查询该对话的消息数量
            Integer messageCount = agentMessageMapper.countByConversationId(conversation.getId(), tenantId);
            conversation.setMessageCount(messageCount);
        }

        return result;
    }
}
