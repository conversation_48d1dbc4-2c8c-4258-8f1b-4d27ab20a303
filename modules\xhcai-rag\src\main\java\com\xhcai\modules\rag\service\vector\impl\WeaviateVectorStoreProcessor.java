package com.xhcai.modules.rag.service.vector.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xhcai.modules.rag.dto.KnowledgeVectorQueryDTO;
import com.xhcai.modules.rag.entity.Dataset;
import com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig;
import com.xhcai.modules.rag.entity.VectorDatabase;
import com.xhcai.modules.rag.mapper.DatasetMapper;
import com.xhcai.modules.rag.mapper.VectorDatabaseMapper;
import com.xhcai.modules.rag.service.IEmbeddingService;
import com.xhcai.modules.rag.service.vector.AbstractVectorStoreProcessor;
import com.xhcai.modules.rag.vo.DocumentSegmentVO;
import io.weaviate.client.Config;
import io.weaviate.client.WeaviateAuthClient;
import io.weaviate.client.WeaviateClient;
import io.weaviate.client.base.Result;
import io.weaviate.client.v1.data.model.WeaviateObject;
import io.weaviate.client.v1.graphql.model.GraphQLResponse;
import java.util.ArrayList;
import java.util.Objects;

import io.weaviate.client.v1.misc.model.VectorIndexConfig;
import io.weaviate.client.v1.schema.model.Property;
import io.weaviate.client.v1.schema.model.Schema;
import io.weaviate.client.v1.schema.model.WeaviateClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class WeaviateVectorStoreProcessor extends AbstractVectorStoreProcessor {
    @Autowired
    private VectorDatabaseMapper vectorDatabaseMapper;
    @Autowired
    private IEmbeddingService embeddingService;
    @Autowired
    private DatasetMapper ddatasetMapper;
    /**
     * Weaviate 客户端缓存
     */
    private final Map<String, WeaviateClient> weaviateClientMap = new Hashtable<>();


    @Override
    public String getSupportedVectorDbType() {
        return "weaviate";
    }

    private WeaviateClient getWeaviateClient(String vectorDbId) {
        return weaviateClientMap.computeIfAbsent(vectorDbId, k -> createClient(vectorDbId));
    }

    /**
     * {
     *     "sslEnabled": "N",
     *     "caCertPath": "",
     *     "clientCert": "",
     *     "clientKey": "",
     *     "apiKey": "WEAVIATE_API_KEY=WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih",
     *     "token": "",
     *     "connectionTimeout": 30,
     *     "readTimeout": 60
     * }
     */
    private WeaviateClient createClient(String vectorDatabaseId) {
        try {
            VectorDatabase vectorDatabase = vectorDatabaseMapper.selectById(vectorDatabaseId);
            JSONObject jsonConnectionConfig = JSONObject.parse(vectorDatabase.getConnectionConfig());

            String protocol = jsonConnectionConfig.getString("sslEnabled").equals("Y") ? "https" : "http";
            String host = concatIpPort(vectorDatabase.getHost(), vectorDatabase.getPort());
            String apiKey = jsonConnectionConfig.getString("apiKey");
            int connectionTimeout = jsonConnectionConfig.getIntValue("connectionTimeout", 30);
            int readTimeout = jsonConnectionConfig.getIntValue("readTimeout", 60);

            // 如果启用了认证，例如 API Key
            Config config = new Config(protocol, host, null, connectionTimeout, readTimeout, readTimeout);

            // 创建 Weaviate 客户端
            if(StrUtil.isBlank(apiKey)){
                return new WeaviateClient(new Config(protocol, host));
            }else {
                return WeaviateAuthClient.apiKey(config, apiKey);
            }
        }catch (Exception e){
            log.error("创建 Weaviate 客户端失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * @param vectorDbId    向量库ID
     * @param datasetId     知识库ID
     */
    @Override
    public void createIndexIfNotExists(String vectorDbId, String datasetId) {
        WeaviateClient weaviateClient = getWeaviateClient(vectorDbId);

        String className = getIndexName(datasetId);

        // 检查类是否存在，如果不存在就创建 schema
        Result<Schema> schemaResult = weaviateClient.schema().getter().run();
        if(schemaResult.hasErrors()){
            log.error("获取 Weaviate schema 失败: {}", schemaResult.getError().getMessages());
            return;
        }
        Schema schema = schemaResult.getResult();
        boolean classExists = false;
        for (WeaviateClass weaviateClass : schema.getClasses()) {
            if (weaviateClass.getClassName().equals(className)) {
                classExists = true;
                break;
            }
        }

        if(classExists)
            return;

        VectorIndexConfig vectorIndexConfig = VectorIndexConfig.builder()
                .distance("cosine")
                .build();

        // 类不存在，创建 schema
        WeaviateClass build = WeaviateClass.builder()
                .className(className)
                .vectorizer("none")
                .vectorIndexType("hnsw")
                .vectorIndexConfig(vectorIndexConfig)
                .properties(
                        List.of(Property.builder().name("dataset_id").dataType(Collections.singletonList("string")).indexFilterable(true).indexSearchable(false).description("知识库ID").build(),
                                Property.builder().name("tenant_id").dataType(Collections.singletonList("string")).indexFilterable(true).indexSearchable(false).description("租户ID").build(),
                                Property.builder().name("document_id").dataType(Collections.singletonList("string")).indexFilterable(true).indexSearchable(false).description("文档ID").build(),
                                Property.builder().name("position").dataType(Collections.singletonList("int")).indexFilterable(true).indexSearchable(false).description("分段排序号").build(),
                                Property.builder().name("content").dataType(Collections.singletonList("text")).indexFilterable(true).indexSearchable(true).description("原始内容").build(),
                                Property.builder().name("keywords").dataType(Collections.singletonList("text[]")).indexFilterable(true).indexSearchable(true).description("关键词").build(),
                                Property.builder().name("enabled").dataType(Collections.singletonList("boolean")).indexFilterable(true).indexSearchable(false).description("是否启用 1:启用 0:停用").build(),
                                Property.builder().name("create_by").dataType(Collections.singletonList("string")).indexFilterable(true).indexSearchable(false).description("创建人").build(),
                                Property.builder().name("doc_type").dataType(Collections.singletonList("string")).indexFilterable(true).indexSearchable(false).description("文档类型").build(),
                                Property.builder().name("name").dataType(Collections.singletonList("text")).indexFilterable(true).indexSearchable(true).description("文档名称").description("文件名称").build()
                        )
                )
                .build();
        Result<Boolean> createResult = weaviateClient.schema().classCreator().withClass(build).run();
        if (createResult.hasErrors()) {
            log.error("Schema 创建失败: {}", createResult.getError());
        } else {
            log.info("Schema 创建成功: {}", className);
        }
    }

    @Override
    public com.xhcai.common.api.response.Result<String> storeEmbeddings(DocumentSegmentVO documentSegmentVO) {
        String datasetId = documentSegmentVO.getDatasetId();
        String className = getIndexName(datasetId);
        String vectorDatabaseId = documentSegmentVO.getKnowledgeVectorizationConfig().getVectorDatabaseId();
        String id = documentSegmentVO.getId();

        // 确保 schema 存在
        createIndexIfNotExists(vectorDatabaseId, datasetId);

        WeaviateClient weaviateClient = getWeaviateClient(vectorDatabaseId);

        // 1) 取得向量
        float[] vectorContent = documentSegmentVO.getVector();
        if (vectorContent == null || vectorContent.length == 0) {
            log.warn("分段 position={} 缺少向量内容，已跳过", documentSegmentVO.getPosition());
            return com.xhcai.common.api.response.Result.success("分段缺少向量内容，已跳过");
        }

        // 2) 组装属性（keywords 直接以 List<String> 形式写入 text[] 属性）
        Map<String, Object> props = Map.of(
                "dataset_id", documentSegmentVO.getDatasetId(),
                "tenant_id", documentSegmentVO.getTenantId(),
                "document_id", documentSegmentVO.getDocumentId(),
                "enabled", documentSegmentVO.getEnabled(),
                "create_by", documentSegmentVO.getDocument().getCreateBy(),
                "doc_type", documentSegmentVO.getDocument().getDocType(),
                "name", documentSegmentVO.getDocument().getName(),
                "position", documentSegmentVO.getPosition(),
                "content", documentSegmentVO.getContent(),
                "keywords", documentSegmentVO.getKeywords()
        );

        // 3) float[] -> Float[]
        Float[] vector = new Float[vectorContent.length];
        for (int i = 0; i < vectorContent.length; i++) vector[i] = vectorContent[i];

        // 4) 显式设定 ID（来自 documentSegmentVO.getId()），并携带向量写入
        try {
            Result<WeaviateObject> result = weaviateClient.data().creator()
                    .withClassName(className)
                    .withID(id)
                    .withProperties(props)
                    .withVector(vector)
                    .run();
            if (result.hasErrors()) {
                log.error("向量入库失败: datasetId={}, position={}, error={}", datasetId, documentSegmentVO.getPosition(), result.getError());
                return com.xhcai.common.api.response.Result.error("向量入库失败: " + result.getError());
            } else {
                log.info("向量入库成功: datasetId={}, position={}", datasetId, documentSegmentVO.getPosition());
                return com.xhcai.common.api.response.Result.success("向量入库成功");
            }
        } catch (Exception e) {
            log.error("向量入库异常: datasetId={}, position={}, msg={}", datasetId, documentSegmentVO.getPosition(), e.getMessage(), e);
            return com.xhcai.common.api.response.Result.error("向量入库异常: " + e.getMessage());
        }
    }

    @Override
    public List<String> listVector(KnowledgeVectorQueryDTO knowledgeVectorQueryDTO) {
        String datasetId = knowledgeVectorQueryDTO.getDatasetId();
        String className = getIndexName(datasetId);

        // 如果缺少向量数据库ID或者向量模型ID，则根据datasetId从数据库中获取
        if(StrUtil.isBlank(  knowledgeVectorQueryDTO.getVectorDbId() ) || StrUtil.isBlank(knowledgeVectorQueryDTO.getVectorModelId())){
            Dataset dataset = ddatasetMapper.selectById(datasetId);
            knowledgeVectorQueryDTO.setVectorDbId(dataset.getVectorDatabaseId());
            Map<String, Object> vectorizationConfig = dataset.getVectorizationConfig();
            KnowledgeVectorizationConfig knowledgeVectorizationConfig = BeanUtil.toBean(vectorizationConfig, KnowledgeVectorizationConfig.class);
            knowledgeVectorQueryDTO.setVectorModelId(knowledgeVectorizationConfig.getVectorDatabaseId());
        }

        WeaviateClient weaviateClient = getWeaviateClient(knowledgeVectorQueryDTO.getVectorDbId());
        if (weaviateClient == null) {
            log.error("Weaviate 客户端未初始化: vectorDbId={}", knowledgeVectorQueryDTO.getVectorDbId());
            return List.of();
        }

        // 若无任何查询条件，直接返回空，避免随机返回
        boolean hasFilter = StrUtil.isNotBlank(knowledgeVectorQueryDTO.getQuery())
                || StrUtil.isNotBlank(knowledgeVectorQueryDTO.getTenantId())
                || StrUtil.isNotBlank(knowledgeVectorQueryDTO.getDocumentId())
                || StrUtil.isNotBlank(knowledgeVectorQueryDTO.getEnabled())
                || StrUtil.isNotBlank(knowledgeVectorQueryDTO.getCreateBy())
                || StrUtil.isNotBlank(knowledgeVectorQueryDTO.getDocType())
                || StrUtil.isNotBlank(knowledgeVectorQueryDTO.getName())
                || Objects.nonNull(knowledgeVectorQueryDTO.getPosition())
                || StrUtil.isNotBlank(knowledgeVectorQueryDTO.getKeyword());
        if (!hasFilter) {
            return List.of();
        }

        // 构建过滤条件（where）
        List<io.weaviate.client.v1.filters.WhereFilter> operands = new ArrayList<>();
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getTenantId())) {
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"tenant_id"})
                    .operator(io.weaviate.client.v1.filters.Operator.Equal)
                    .valueText(knowledgeVectorQueryDTO.getTenantId())
                    .build());
        }
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getDocumentId())) {
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"document_id"})
                    .operator(io.weaviate.client.v1.filters.Operator.Equal)
                    .valueText(knowledgeVectorQueryDTO.getDocumentId())
                    .build());
        }
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getEnabled())) {
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"enabled"})
                    .operator(io.weaviate.client.v1.filters.Operator.Equal)
                    .valueText(knowledgeVectorQueryDTO.getEnabled())
                    .build());
        }
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getCreateBy())) {
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"create_by"})
                    .operator(io.weaviate.client.v1.filters.Operator.Equal)
                    .valueText(knowledgeVectorQueryDTO.getCreateBy())
                    .build());
        }
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getDocType())) {
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"doc_type"})
                    .operator(io.weaviate.client.v1.filters.Operator.Equal)
                    .valueText(knowledgeVectorQueryDTO.getDocType())
                    .build());
        }
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getName())) {
            // 文本字段使用模糊匹配，更友好
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"name"})
                    .operator(io.weaviate.client.v1.filters.Operator.Like)
                    .valueText("*" + knowledgeVectorQueryDTO.getName() + "*")
                    .build());
        }
        if (Objects.nonNull(knowledgeVectorQueryDTO.getPosition())) {
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"position"})
                    .operator(io.weaviate.client.v1.filters.Operator.Equal)
                    .valueInt(knowledgeVectorQueryDTO.getPosition())
                    .build());
        }
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getKeyword())) {
            // keywords 为 text[]，使用 ContainsAny；单值也可放入数组
            operands.add(io.weaviate.client.v1.filters.WhereFilter.builder()
                    .path(new String[]{"keywords"})
                    .operator(io.weaviate.client.v1.filters.Operator.ContainsAny)
                    .valueText(knowledgeVectorQueryDTO.getKeyword())
                    .build());
        }

        io.weaviate.client.v1.filters.WhereFilter where = null;
        if (!operands.isEmpty()) {
            if (operands.size() == 1) {
                where = operands.get(0);
            } else {
                where = io.weaviate.client.v1.filters.WhereFilter.builder()
                        .operator(io.weaviate.client.v1.filters.Operator.And)
                        .operands(operands.toArray(new io.weaviate.client.v1.filters.WhereFilter[0]))
                        .build();
            }
        }

        // 需要返回的字段
        io.weaviate.client.v1.graphql.query.fields.Field content = io.weaviate.client.v1.graphql.query.fields.Field.builder().name("content").build();
        io.weaviate.client.v1.graphql.query.fields.Field additional = io.weaviate.client.v1.graphql.query.fields.Field.builder()
                .name("_additional")
                .fields(new io.weaviate.client.v1.graphql.query.fields.Field[]{
                        io.weaviate.client.v1.graphql.query.fields.Field.builder().name("id").build(),
                        io.weaviate.client.v1.graphql.query.fields.Field.builder().name("distance").build(),
                        io.weaviate.client.v1.graphql.query.fields.Field.builder().name("name").build(),
                        io.weaviate.client.v1.graphql.query.fields.Field.builder().name("position").build()
                })
                .build();

        var getBuilder = weaviateClient.graphQL().get()
                .withClassName(className)
                .withFields(content, additional)
                .withLimit(knowledgeVectorQueryDTO.getMaxResults() == null ? 5 : knowledgeVectorQueryDTO.getMaxResults());

        if (where != null) {
            getBuilder = getBuilder.withWhere(where);
        }

        // 向量化查询文本（nearVector）
        if (StrUtil.isNotBlank(knowledgeVectorQueryDTO.getQuery())) {
            try {
                float[] vec = embeddingService.embedText(knowledgeVectorQueryDTO.getVectorModelId(), knowledgeVectorQueryDTO.getQuery());
                if (vec != null && vec.length > 0) {
                    Float[] vector = new Float[vec.length];
                    for (int i = 0; i < vec.length; i++) vector[i] = vec[i];
                    io.weaviate.client.v1.graphql.query.argument.NearVectorArgument nearVector = io.weaviate.client.v1.graphql.query.argument.NearVectorArgument
                            .builder()
                            .vector(vector)
                            .build();
                    getBuilder = getBuilder.withNearVector(nearVector);
                }
            } catch (Exception e) {
                log.error("查询向量化失败，改为仅过滤检索: {}", e.getMessage());
            }
        }

        Result<GraphQLResponse> result = getBuilder.run();
        if (result == null || result.hasErrors()) {
            log.error("Weaviate 查询失败: {}", result == null ? "null" : result.getError());
            return List.of();
        }

        try {
            // 解析响应，提取 _additional.id
            Object dataObj = result.getResult();
            String jsonStr = String.valueOf(dataObj);
            com.alibaba.fastjson2.JSONObject root = com.alibaba.fastjson2.JSONObject.parseObject(jsonStr);
            com.alibaba.fastjson2.JSONObject data = root.getJSONObject("data");
            if (data == null) return List.of();
            com.alibaba.fastjson2.JSONObject get = data.getJSONObject("Get");
            if (get == null) return List.of();
            com.alibaba.fastjson2.JSONArray arr = get.getJSONArray(className);
            if (arr == null || arr.isEmpty()) return List.of();

            List<String> ids = new ArrayList<>();
            for (int i = 0; i < arr.size(); i++) {
                com.alibaba.fastjson2.JSONObject obj = arr.getJSONObject(i);
                com.alibaba.fastjson2.JSONObject add = obj.getJSONObject("_additional");
                if (add != null && add.containsKey("id")) {
                    ids.add(add.getString("id"));
                }
            }
            return ids;
        } catch (Exception e) {
            log.error("解析 Weaviate 响应失败: {}", e.getMessage(), e);
            return List.of();
        }
    }



    @Override
    public void removeByDatasetId(String vectorDbId, String datasetId) {

    }

    @Override
    public void removeByDocId(String vectorDbId, String datasetId, String docId) {

    }
}
