package com.xhcai.modules.dify.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * Dify智能体克隆请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify智能体克隆请求")
public class DifyAgentCloneRequestDTO {

    /**
     * 源智能体ID
     */
    @Schema(description = "源智能体ID", example = "9dc52c66-6e7d-45c1-9ec3-5037b8ef171a", required = true)
    @NotBlank(message = "源智能体ID不能为空")
    private String sourceAppId;

    /**
     * 新智能体名称
     */
    @Schema(description = "新智能体名称", example = "客服助手副本", required = true)
    @NotBlank(message = "智能体名称不能为空")
    @Size(min = 1, max = 100, message = "智能体名称长度必须在1-100个字符之间")
    private String name;

    /**
     * 图标类型
     */
    @Schema(description = "图标类型", example = "emoji", allowableValues = {"emoji", "image"})
    @Pattern(regexp = "^(emoji|image)$", message = "图标类型必须为emoji或image")
    private String iconType = "emoji";

    /**
     * 图标
     */
    @Schema(description = "图标", example = "🤖")
    private String icon = "🤖";

    /**
     * 图标背景色
     */
    @Schema(description = "图标背景色", example = "#FFEAD5")
    private String iconBackground = "#FFEAD5";

    /**
     * 智能体模式
     */
    @Schema(description = "智能体模式", example = "workflow", allowableValues = {"chat", "workflow", "completion", "advanced-chat", "agent-chat"})
    @NotBlank(message = "智能体模式不能为空")
    @Pattern(regexp = "^(chat|workflow|completion|advanced-chat|agent-chat)$", message = "智能体模式必须为chat、workflow、completion、advanced-chat或agent-chat")
    private String mode;

    /**
     * 平台ID
     */
    @Schema(description = "平台ID", example = "platform-001", required = true)
    @NotBlank(message = "平台ID不能为空")
    private String platformId;

    // Getters and Setters
    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconType() {
        return iconType;
    }

    public void setIconType(String iconType) {
        this.iconType = iconType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBackground() {
        return iconBackground;
    }

    public void setIconBackground(String iconBackground) {
        this.iconBackground = iconBackground;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        return "DifyAgentCloneRequestDTO{" +
                "sourceAppId='" + sourceAppId + '\'' +
                ", name='" + name + '\'' +
                ", iconType='" + iconType + '\'' +
                ", icon='" + icon + '\'' +
                ", iconBackground='" + iconBackground + '\'' +
                ", mode='" + mode + '\'' +
                ", platformId='" + platformId + '\'' +
                '}';
    }
}
