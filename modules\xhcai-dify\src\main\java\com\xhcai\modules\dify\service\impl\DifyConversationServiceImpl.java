package com.xhcai.modules.dify.service.impl;

import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.enums.ResultCode;
import com.xhcai.modules.dify.config.DifyWebClientConfig;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationNameResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyMessageListResponseDTO;
import com.xhcai.modules.dify.service.IDifyAuthService;
import com.xhcai.modules.dify.service.IDifyConversationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.util.ArrayList;

/**
 * Dify会话服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class DifyConversationServiceImpl implements IDifyConversationService {

    private static final Logger log = LoggerFactory.getLogger(DifyConversationServiceImpl.class);

    @Autowired
    private DifyWebClientConfig difyWebClientConfig;

    @Autowired(required = false)
    private IDifyAuthService difyAuthService;

    @Override
    public DifyConversationListResponseDTO getConversations(String appId, Integer limit, Boolean pinned) {
        log.info("获取智能体会话列表: appId={}, limit={}, pinned={}", appId, limit, pinned);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (difyAuthService == null) {
            log.error("认证服务未启用，无法调用 Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "认证服务未启用");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法获取会话列表");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        try {
            // 第一步：获取已安装应用列表
            log.info("开始获取已安装应用列表");
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig
                    .getInstalledAppsWithAuth()
                    .block(); // 同步调用

            if (installedAppsResponse == null || installedAppsResponse.getInstalledApps() == null) {
                log.error("获取已安装应用列表失败或为空");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            log.info("成功获取已安装应用列表，应用数量: {}",
                installedAppsResponse.getInstalledApps().size());

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.findInstalledAppIdByAppId(appId);
            if (installedAppId == null) {
                log.error("未找到应用ID {} 对应的已安装应用", appId);
                throw new BusinessException(ResultCode.NOT_FOUND.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {}", installedAppId);

            // 第二步：直接调用会话列表API（内联实现）
            DifyConversationListResponseDTO conversationResponse = getConversationsWithAuthInternal(installedAppId, limit, pinned, 0);

            if (conversationResponse == null) {
                log.warn("获取会话列表为空");
                conversationResponse = new DifyConversationListResponseDTO();
                conversationResponse.setData(new ArrayList<>());
                conversationResponse.setHasMore(false);
                conversationResponse.setTotal(0);
            }

            log.info("成功获取会话列表，会话数量: {}",
                conversationResponse.getData() != null ? conversationResponse.getData().size() : 0);

            return conversationResponse;

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("获取会话列表时发生未知异常", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取会话列表失败: " + e.getMessage());
        }
    }

    @Override
    public DifyConversationListResponseDTO getChatConversations(String appId, Integer page, Integer limit,
                                                                String start, String end, String sortBy, String annotationStatus) {
        log.info("获取会话列表: appId={}, page={}, limit={}, start={}, end={}, sortBy={}, annotationStatus={}",
                appId, page, limit, start, end, sortBy, annotationStatus);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (difyAuthService == null) {
            log.error("认证服务未启用，无法调用 Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "认证服务未启用");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法获取会话列表");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        try {
            // 设置默认值
            Integer finalPage = page != null ? page : 1;
            Integer finalLimit = limit != null ? limit : 10;
            String finalSortBy = StringUtils.hasText(sortBy) ? sortBy : "-created_at";
            String finalAnnotationStatus = StringUtils.hasText(annotationStatus) ? annotationStatus : "all";

            // 调用新的会话列表API
            DifyConversationListResponseDTO conversationResponse = difyWebClientConfig
                    .getChatConversationsWithAuth(appId, finalPage, finalLimit, start, end, finalSortBy, finalAnnotationStatus)
                    .block(); // 同步调用

            if (conversationResponse == null) {
                log.warn("获取会话列表为空");
                conversationResponse = new DifyConversationListResponseDTO();
                conversationResponse.setPage(finalPage);
                conversationResponse.setLimit(finalLimit);
                conversationResponse.setData(new ArrayList<>());
                conversationResponse.setHasMore(false);
                conversationResponse.setTotal(0);
            }

            log.info("成功获取会话列表，会话数量: {}",
                conversationResponse.getData() != null ? conversationResponse.getData().size() : 0);

            return conversationResponse;

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("获取会话列表时发生未知异常", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取会话列表失败: " + e.getMessage());
        }
    }

    @Override
    public DifyConversationDTO updateConversationName(String appId, String conversationId, String name) {
        log.info("修改会话名称: appId={}, conversationId={}, name={}", appId, conversationId, name);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId) || !StringUtils.hasText(conversationId) || !StringUtils.hasText(name)) {
            log.error("应用ID、会话ID或名称未提供");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID、会话ID和名称不能为空");
        }

        try {
            // 第一步：获取已安装应用列表
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig
                    .getInstalledAppsWithAuth()
                    .block();

            if (installedAppsResponse == null) {
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.findInstalledAppIdByAppId(appId);
            if (installedAppId == null) {
                throw new BusinessException(ResultCode.NOT_FOUND.getCode(), "未找到对应的已安装应用");
            }

            // 第二步：修改会话名称
            DifyConversationNameResponseDTO nameResponse = difyWebClientConfig
                    .updateConversationNameWithAuth(installedAppId, conversationId, name)
                    .block();

            if (nameResponse == null) {
                throw new BusinessException(ResultCode.FAIL.getCode(), "修改会话名称失败");
            }

            // 转换为 DifyConversationDTO
            return convertToConversationDTO(nameResponse);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改会话名称时发生未知异常", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "修改会话名称失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteConversation(String appId, String conversationId) {
        log.info("删除会话记录: appId={}, conversationId={}", appId, conversationId);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId) || !StringUtils.hasText(conversationId)) {
            log.error("应用ID或会话ID未提供");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID和会话ID不能为空");
        }

        try {
            // 第一步：获取已安装应用列表
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig
                    .getInstalledAppsWithAuth()
                    .block();

            if (installedAppsResponse == null) {
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.findInstalledAppIdByAppId(appId);
            if (installedAppId == null) {
                throw new BusinessException(ResultCode.NOT_FOUND.getCode(), "未找到对应的已安装应用");
            }

            // 第二步：删除会话记录
            Boolean deleteResult = difyWebClientConfig
                    .deleteConversationWithAuth(installedAppId, conversationId)
                    .block();

            return Boolean.TRUE.equals(deleteResult);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除会话记录时发生未知异常", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "删除会话记录失败: " + e.getMessage());
        }
    }

    @Override
    public DifyMessageListResponseDTO getConversationMessages(String appId, String conversationId, Integer limit, String lastId) {
        log.info("获取会话消息列表: appId={}, conversationId={}, limit={}, lastId={}", 
            appId, conversationId, limit, lastId);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId) || !StringUtils.hasText(conversationId)) {
            log.error("应用ID或会话ID未提供");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID和会话ID不能为空");
        }

        try {
            // 第一步：获取已安装应用列表
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig
                    .getInstalledAppsWithAuth()
                    .block();

            if (installedAppsResponse == null) {
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.findInstalledAppIdByAppId(appId);
            if (installedAppId == null) {
                throw new BusinessException(ResultCode.NOT_FOUND.getCode(), "未找到对应的已安装应用");
            }

            // 第二步：获取会话消息列表
            DifyMessageListResponseDTO messagesResponse = difyWebClientConfig
                    .getConversationMessagesRawWithAuth(installedAppId, conversationId, limit, lastId)
                    .block();

            if (messagesResponse == null) {
                log.warn("获取会话消息列表为空");
                messagesResponse = new DifyMessageListResponseDTO();
                messagesResponse.setData(new ArrayList<>());
                messagesResponse.setHasMore(false);
                messagesResponse.setLimit(limit);
                messagesResponse.setTotal(0);
            }

            return messagesResponse;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取会话消息列表时发生未知异常", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取会话消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 转换 DifyConversationNameResponseDTO 为 DifyConversationDTO
     *
     * @param nameResponse 会话名称响应DTO
     * @return 会话DTO
     */
    private DifyConversationDTO convertToConversationDTO(DifyConversationNameResponseDTO nameResponse) {
        DifyConversationDTO conversationDTO = new DifyConversationDTO();
        conversationDTO.setId(nameResponse.getId());
        conversationDTO.setName(nameResponse.getName());
        conversationDTO.setStatus(nameResponse.getStatus());
        conversationDTO.setIntroduction(nameResponse.getIntroduction());
        conversationDTO.setCreatedAt(nameResponse.getCreatedAt());
        conversationDTO.setUpdatedAt(nameResponse.getUpdatedAt());
        return conversationDTO;
    }

    /**
     * 获取智能体会话列表（内联实现，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param limit 限制数量，默认100
     * @param pinned 是否只获取置顶会话，默认false
     * @param retryCount 重试次数
     * @return 会话列表响应
     */
    private DifyConversationListResponseDTO getConversationsWithAuthInternal(String installedAppId, Integer limit, Boolean pinned, int retryCount) {
        if (retryCount > 2) {
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "API 调用重试次数超限");
        }

        // 构建查询参数
        StringBuilder uriBuilder = new StringBuilder("/console/api/installed-apps/")
                .append(installedAppId)
                .append("/conversations");

        boolean hasParams = false;
        if (limit != null) {
            uriBuilder.append(hasParams ? "&" : "?").append("limit=").append(limit);
            hasParams = true;
        }
        if (pinned != null) {
            uriBuilder.append(hasParams ? "&" : "?").append("pinned=").append(pinned);
        }

        String uri = uriBuilder.toString();
        log.debug("构建会话列表请求URI: {}", uri);

        try {
            // 获取访问令牌并发起请求
            Mono<DifyConversationListResponseDTO> responseMono = difyAuthService.getValidAccessToken()
                    .flatMap(accessToken -> {
                        if (!StringUtils.hasText(accessToken)) {
                            return Mono.error(new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "访问令牌为空"));
                        }

                        log.debug("使用访问令牌调用会话列表API (重试次数: {}): {}", retryCount, uri);

                        return difyWebClientConfig.difyWebClient()
                                .get()
                                .uri(uri)
                                .header("Authorization", "Bearer " + accessToken)
                                .header("Content-Type", "application/json")
                                .retrieve()
                                .onStatus(status -> status.value() == 401, response -> {
                                    log.warn("收到 401 错误，检查是否为令牌过期");
                                    return response.bodyToMono(String.class)
                                            .map(body -> {
                                                log.debug("401 错误响应体: {}", body);
                                                return new WebClientResponseException(401, "Token Expired", null, body.getBytes(), null);
                                            });
                                })
                                .onStatus(status -> status.value() == 600, response -> {
                                    log.warn("收到 600 错误，需要重新登录");
                                    return Mono.error(new WebClientResponseException(600, "Need Relogin", null, null, null));
                                })
                                .bodyToMono(DifyConversationListResponseDTO.class);
                    })
                    .doOnNext(response -> log.debug("收到会话列表响应: {}", response))
                    .doOnError(error -> log.error("获取会话列表失败", error))
                    .onErrorResume(WebClientResponseException.class, e -> {
                        if (e.getStatusCode().value() == 401) {
                            log.warn("处理 401 错误：刷新令牌并重试");
                            return difyAuthService.handleUnauthorized()
                                    .flatMap(newAccessToken -> Mono.fromCallable(() ->
                                            getConversationsWithAuthInternal(installedAppId, limit, pinned, retryCount + 1)));

                        } else if (e.getStatusCode().value() == 600) {
                            log.warn("处理 600 错误：重新登录并重试");
                            return difyAuthService.handleRelogin()
                                    .flatMap(newAccessToken -> Mono.fromCallable(() ->
                                            getConversationsWithAuthInternal(installedAppId, limit, pinned, retryCount + 1)));

                        } else {
                            return Mono.error(e);
                        }
                    });

            return responseMono.block(); // 同步调用

        } catch (Exception e) {
            log.error("调用会话列表API时发生异常", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "调用会话列表API失败: " + e.getMessage());
        }
    }
}
