package com.xhcai.modules.rag.service;

import cn.hutool.core.io.FileUtil;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import com.xhcai.modules.rag.service.segmentation.impl.PdfFileSegmentationProcessor;
import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedInputStream;
import java.util.List;


public class PdfFileSegmentationTest {

    // 从AbstractFileSegmentationProcessor获取常量值

    public static void main(String[] args) {
        // ✅ 配置日志级别，让debug日志在控制台输出
        Logger rootLogger = (Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
        rootLogger.setLevel(Level.DEBUG);

        // 也可以只针对特定的类设置debug级别
        Logger pdfProcessorLogger = (Logger) LoggerFactory.getLogger(PdfFileSegmentationProcessor.class);
        pdfProcessorLogger.setLevel(Level.DEBUG);


        PdfFileSegmentationProcessor processor = new PdfFileSegmentationProcessor();
        Document document = new Document();
        document.setId("xinghuo-pdf-001");
        document.setName("xinghuo.pdf");

        try {
            BufferedInputStream inputStream = FileUtil.getInputStream("D:\\data\\xinghuo.pdf");
            String text = processor.extractTextFromPdf(document, inputStream);
            System.out.println("=== PDF文本提取结果 ===");
            System.out.println("文档总字符数: " + text.length());
            System.out.println("文档内容预览: " + (text.length() > 200 ? text.substring(0, 200) + "..." : text));

            // 配置固定长度分段参数
            SegmentConfig.ConstantLengthConfig constantLength = new SegmentConfig.ConstantLengthConfig();
            constantLength.setMaxLen(800);  // 设置分段最大长度为800字符
            constantLength.setOverlapLen(80); // 设置重叠长度为80字符

            System.out.println("\n=== 分段配置 ===");
            System.out.println("分段最大长度: " + constantLength.getMaxLen());
            System.out.println("重叠长度: " + constantLength.getOverlapLen());

            // 执行固定大小分段
            List<SegmentResult> segmentResults = processor.segmentByFixedSize(text, constantLength);

            System.out.println("\n=== 分段结果 ===");
            System.out.println("共生成分段数量: " + segmentResults.size());

            // 验证分段结果
            int totalChars = 0;
            int oversizeCount = 0;
            int maxSize = 0;
            int minSize = Integer.MAX_VALUE;

            for (int i = 0; i < segmentResults.size(); i++) {
                SegmentResult segment = segmentResults.get(i);
                int size = segment.getContent().length();
                totalChars += size;

                if (size > constantLength.getMaxLen()) {
                    oversizeCount++;
                }
                maxSize = Math.max(maxSize, size);
                minSize = Math.min(minSize, size);

                System.out.println("\n--- 分段 " + (i + 1) + " ---");
                System.out.println("位置: " + segment.getPosition());
                System.out.println("字符数: " + size +
                    (size > constantLength.getMaxLen() ? " ⚠️超出限制" : " ✅符合限制"));
                System.out.println("关键词: " + segment.getKeywords());
                System.out.println("内容预览: " +
                    (segment.getContent().length() > 100 ?
                        segment.getContent().substring(0, 100) + "..." :
                        segment.getContent()));
            }

            System.out.println("\n=== 分段统计 ===");
            System.out.println("原文总字符数: " + text.length());
            System.out.println("分段总字符数: " + totalChars);
            System.out.println("内容覆盖率: " + String.format("%.2f%%", (double) totalChars / text.length() * 100));
            System.out.println("超出限制的分段数: " + oversizeCount);
            System.out.println("最大分段大小: " + maxSize);
            System.out.println("最小分段大小: " + minSize);
            System.out.println("平均分段大小: " + (segmentResults.isEmpty() ? 0 : totalChars / segmentResults.size()));

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
