package com.xhcai.common.security.enums;

import lombok.Getter;

/**
 * 认证类型
 */
@Getter
public enum SecurityType {

    TOKEN("token", "Token认证"),
    API_KEY("apiKey", "APIKey认证");

    private final String code;
    private final String info;

    SecurityType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 根据代码获取枚举
     */
    public static SecurityType getByCode(String code) {
        for (SecurityType value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
