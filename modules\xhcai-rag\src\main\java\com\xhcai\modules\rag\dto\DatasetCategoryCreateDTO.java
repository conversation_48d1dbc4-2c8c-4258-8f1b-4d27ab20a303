package com.xhcai.modules.rag.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 文档分类创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档分类创建DTO")
@Data
public class DatasetCategoryCreateDTO {

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID", example = "dataset123")
    @NotBlank(message = "知识库ID不能为空")
    @Size(max = 32, message = "知识库ID长度不能超过32个字符")
    private String datasetId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "产品文档")
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String name;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述", example = "产品相关的文档资料")
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    private String description;

    /**
     * 数据源类型
     */
    @Schema(description = "数据源类型", example = "1")
    private String datasetType;

    /**
     * 父分类ID
     */
    @Schema(description = "父分类ID", example = "parent123")
    private String parentId;

    /**
     * 排序号
     */
    @Schema(description = "排序号", example = "1")
    private Integer sortOrder;
}
