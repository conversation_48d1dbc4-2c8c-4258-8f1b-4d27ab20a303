package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.modules.rag.service.IOnlyOfficeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * OnlyOffice控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/onlyoffice")
@Tag(name = "OnlyOffice文档预览", description = "OnlyOffice文档预览相关接口")
public class OnlyOfficeController {

    @Autowired
    private IOnlyOfficeService onlyOfficeService;

    @Operation(summary = "获取文档配置", description = "获取OnlyOffice文档预览配置")
    @GetMapping("/config/{documentId}")
    public Result<Map<String, Object>> getDocumentConfig(
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "模式") @RequestParam(defaultValue = "view") String mode) {
        
        log.info("获取OnlyOffice文档配置: documentId={}, mode={}", documentId, mode);

        try {
            Map<String, Object> config = onlyOfficeService.generateDocumentConfig(documentId, mode);
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取OnlyOffice文档配置失败: documentId={}, error={}", documentId, e.getMessage(), e);
            return Result.fail("获取文档配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查文档格式支持", description = "检查文档格式是否支持预览")
    @GetMapping("/check-format/{format}")
    public Result<Map<String, Boolean>> checkFormat(
            @Parameter(description = "文件格式") @PathVariable String format) {
        
        boolean supported = onlyOfficeService.isSupportedFormat(format);
        boolean editable = onlyOfficeService.isEditableFormat(format);
        
        Map<String, Boolean> result = Map.of(
            "supported", supported,
            "editable", editable
        );
        
        return Result.success(result);
    }
}
