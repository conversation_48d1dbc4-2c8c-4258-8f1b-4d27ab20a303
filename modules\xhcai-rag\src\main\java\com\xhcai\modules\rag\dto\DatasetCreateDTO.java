package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.Map;

/**
 * 知识库创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "知识库创建DTO")
public class DatasetCreateDTO {

    @Schema(description = "知识库名称", example = "技术文档库", required = true)
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 255, message = "知识库名称长度不能超过255个字符")
    private String name;

    @Schema(description = "描述", example = "存储技术相关文档的知识库")
    @Size(max = 1000, message = "描述长度不能超过1000个字符")
    private String description;

    @Schema(description = "数据源类型", example = "document")
    private String dataSourceType;

    @Schema(description = "模型ID", example = "model_123")
    private String modelId;

    @Schema(description = "检索配置", example = "{\"top_k\": 5, \"score_threshold\": 0.7}")
    private Map<String, Object> vectorizationConfig;

    /**
     * 向量数据库ID
     */
    @Schema(description = "向量数据库ID", example = "vector_db_123")
    private String vectorDatabaseId;

    /**
     * 文件存储ID
     */
    @Schema(description = "文件存储ID", example = "file_storage_123")
    private String fileStorageId;

    /**
     * 知识库图标
     */
    @Schema(description = "知识库图标", example = "📚")
    private String icon;

    /**
     * 知识库图标背景色
     */
    @Schema(description = "知识库图标背景色", example = "#3b82f6")
    private String iconBg;

    // ==================== Getters and Setters ====================

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDataSourceType() {
        return dataSourceType;
    }

    public void setDataSourceType(String dataSourceType) {
        this.dataSourceType = dataSourceType;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public Map<String, Object> getVectorizationConfig() {
        return vectorizationConfig;
    }

    public void setVectorizationConfig(Map<String, Object> vectorizationConfig) {
        this.vectorizationConfig = vectorizationConfig;
    }

    public String getVectorDatabaseId() {
        return vectorDatabaseId;
    }

    public void setVectorDatabaseId(String vectorDatabaseId) {
        this.vectorDatabaseId = vectorDatabaseId;
    }

    public String getFileStorageId() {
        return fileStorageId;
    }

    public void setFileStorageId(String fileStorageId) {
        this.fileStorageId = fileStorageId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBg() {
        return iconBg;
    }

    public void setIconBg(String iconBg) {
        this.iconBg = iconBg;
    }

    @Override
    public String toString() {
        return "DatasetCreateDTO{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", dataSourceType='" + dataSourceType + '\'' +
                ", modelId='" + modelId + '\'' +
                ", vectorizationConfig=" + vectorizationConfig + '\'' +
                ", icon=" + getIcon() + '\'' +
                ", iconBg=" + getIconBg() +
                '}';
    }
}
