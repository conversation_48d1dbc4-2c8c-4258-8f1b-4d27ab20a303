package com.xhcai.modules.rag.plugins.rabbitmq.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.vo.DocumentSegmentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.modules.rag.plugins.rabbitmq.model.RabbitMQMessage;
import com.xhcai.modules.rag.service.IDocumentSegmentService;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息处理服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
public class MessageProcessingService {

    @Autowired
    private IDocumentSegmentService documentSegmentService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理文档分段处理消息
     *
     * @param message 消息对象
     * @return 处理结果
     */
    public boolean processDocumentSegmentationMessage(RabbitMQMessage message) {
        try {
            Document document = objectMapper.convertValue(message.getPayload(), Document.class);

            if (document == null) {
                log.error("从MQ中消费到的文档不存在，请检查！！！！！");
                return false;
            }

            String tenantId = message.getTenantId();
            String userId = message.getUserId();

            // 启动异步分段处理
            processDocumentSegmentationAsync(document, tenantId, userId);
            return true;

        } catch (Exception e) {
            log.error("文档分段处理失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 异步处理文档分段
     */
    @Async("documentProcessingExecutor")
    public void processDocumentSegmentationAsync(Document document, String tenantId, String userId) {
        try {
            // 逐个处理文档分段
            try {

                // 调用文档分段服务进行处理
                documentSegmentService.processDocumentSegmentation(document, tenantId, userId);

            } catch (Exception e) {
                log.error("文档分段处理异常: documentId={}, error={}", document.getId(), e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("批量文档分段处理失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
        }
    }

    /**
     * 处理向量化处理消息
     *
     * @param message 消息对象
     * @return 处理结果
     */
    public boolean processEmbeddingMessage(RabbitMQMessage message) {
        try {
            DocumentSegmentVO documentSegmentVO = objectMapper.convertValue(message.getPayload(), DocumentSegmentVO.class);

            if (documentSegmentVO == null) {
                log.error("从MQ中消费到的文档分段不存在，请不符合规范，不进行处理！！！！！");
                return true;
            }
            log.info("向量化处理接收到信息:  datasetID:{}     segmentID:{}", documentSegmentVO.getDatasetId(), documentSegmentVO.getId());
            String tenantId = message.getTenantId();
            String userId = message.getUserId();
            // 逐个处理文档分段向量化
            processDocumentSegmentEmbeddingAsync(documentSegmentVO, tenantId, userId);
            return true;
        } catch (Exception e) {
            log.error("向量化处理失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 异步处理文档分段向量化
     */
    @Async("embeddingProcessingExecutor")
    public void processDocumentSegmentEmbeddingAsync(DocumentSegmentVO documentSegmentVO, String tenantId, String userId) {
        // 逐个处理文档分段向量化
        documentSegmentService.processDocumentSegmentEmbedding(documentSegmentVO, tenantId, userId);
    }

    /**
     * 处理通知消息
     *
     * @param message 消息对象
     * @return 处理结果
     */
    public boolean processNotificationMessage(RabbitMQMessage message) {
        try {
            log.info("开始处理通知: messageId={}, tenantId={}, userId={}",
                    message.getMessageId(), message.getTenantId(), message.getUserId());

            // TODO: 实现具体的通知处理逻辑
            // 1. 解析通知内容
            // 2. 发送通知（邮件、短信、推送等）
            // 3. 记录通知日志
            // 模拟处理时间
            Thread.sleep(200);

            log.info("通知处理完成: messageId={}", message.getMessageId());
            return true;

        } catch (Exception e) {
            log.error("通知处理失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理死信消息
     *
     * @param message 消息对象
     */
    public void processDeadLetterMessage(RabbitMQMessage message) {
        try {
            log.warn("处理死信消息: messageId={}, type={}, retryCount={}, errorMessage={}",
                    message.getMessageId(), message.getMessageType(),
                    message.getRetryCount(), message.getErrorMessage());

            // TODO: 实现死信消息处理逻辑
            // 1. 记录死信消息到数据库
            // 2. 发送告警通知
            // 3. 分析失败原因
            // 4. 可能的人工干预处理
            // 记录死信消息
            recordDeadLetterMessage(message);

            // 发送告警
            sendDeadLetterAlert(message);

        } catch (Exception e) {
            log.error("死信消息处理失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
        }
    }

    /**
     * 记录死信消息
     */
    private void recordDeadLetterMessage(RabbitMQMessage message) {
        try {
            // TODO: 将死信消息记录到数据库
            log.info("死信消息已记录: messageId={}", message.getMessageId());
        } catch (Exception e) {
            log.error("记录死信消息失败: messageId={}", message.getMessageId(), e);
        }
    }

    /**
     * 发送死信告警
     */
    private void sendDeadLetterAlert(RabbitMQMessage message) {
        try {
            // TODO: 发送死信告警通知
            log.warn("死信告警已发送: messageId={}, type={}",
                    message.getMessageId(), message.getMessageType());
        } catch (Exception e) {
            log.error("发送死信告警失败: messageId={}", message.getMessageId(), e);
        }
    }
}
