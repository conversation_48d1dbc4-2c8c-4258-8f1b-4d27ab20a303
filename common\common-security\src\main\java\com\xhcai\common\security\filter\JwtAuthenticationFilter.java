package com.xhcai.common.security.filter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.common.security.enums.SecurityType;
import com.xhcai.common.security.service.ApiKeyUsageService;
import com.xhcai.common.security.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.xhcai.common.security.service.LoginUser;
import com.xhcai.common.security.service.UserCacheService;
import com.xhcai.common.security.utils.JwtUtils;

import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;

/**
 * JWT认证过滤器
 *
 * 从请求头中提取JWT token，验证并设置用户认证信息到SecurityContext
 *
 * 注意：不使用@Component注解，避免与SecurityConfig中的手动注册冲突
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String TOKEN_PREFIX = "Bearer ";
    private static final String API_KEY_PREFIX = "ApiKey ";
    private static final String INNER_API_KEY_PREFIX = "InnerApiKey ";
    private static final long TIME_TOLERANCE_MINUTES = 30;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserCacheService userCacheService;

    @Autowired
    private ApiKeyUsageService apiKeyUsageService;

    @Autowired
    @Qualifier("requestMappingHandlerMapping")
    private HandlerMapping handlerMapping;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        String requestUri = request.getRequestURI();
        log.debug("JWT认证过滤器处理请求: {}", requestUri);

        // 双重检查：如果是公开接口，直接跳过认证处理
        if (isPublicEndpoint(requestUri)) {
            log.debug("检测到公开接口，跳过JWT认证: {}", requestUri);
            filterChain.doFilter(request, response);
            return;
        }

        try {
            long startTime = System.currentTimeMillis();

            // 从请求中获取JWT token 或 ApiKey
            Map<String, String> auth = getAuthFromRequest(request);
            log.debug("从请求中提取的token: {}", auth != null ? "存在" : "不存在");

            if (auth == null) {
                log.debug("请求中未找到JWT token 或 JWT ApiKey");
                filterChain.doFilter(request, response);
                return;
            }

            String tokenOrApiKey = null;
            LoginUser loginUser = null;
            if (auth.containsKey(SecurityType.API_KEY.getCode())) {
                log.debug("开始验证JWT ApiKey");

                // 检查是否需要API密钥认证
                RequiresApiKey requiresApiKey = getRequiresApiKeyAnnotation(request);
                if (requiresApiKey == null) {
                    // 不需要API密钥认证，返回错误
                    handleAuthenticationFailure(response, "该请求无权通过API密钥访问");
                    return;
                }

                tokenOrApiKey = auth.get(SecurityType.API_KEY.getCode());
                if (!StringUtils.hasText(tokenOrApiKey)) {
                    // 没有API密钥且不允许Token认证
                    handleAuthenticationFailure(response, "缺少API密钥");
                    return;
                }

                // 1. 验证复合密钥的时效性并获取原始密钥
                String originalApiKey;
                try {
                    originalApiKey = validateCompositeApiKey(tokenOrApiKey, response);
                    if (originalApiKey == null) {
                        // validateCompositeApiKey已经处理了响应
                        return;
                    }
                } catch (Exception e) {
                    handleAuthenticationFailure(response, "验证复合密钥失败: " + e.getMessage());
                    return;
                }

                // 2. 解析原始API密钥
                Claims claims = jwtUtils.getClaimsFromTokenOrApiKey(originalApiKey);

                // 3. 验证密钥是否过期
                if (claims.getExpiration().before(new Date())) {
                    handleAuthenticationFailure(response, "API密钥已过期");
                    return;
                }

                // 4. 验证目标类型
                String targetType = claims.get("targetType", String.class);
                if (!Arrays.asList(requiresApiKey.targetTypes()).contains(targetType)) {
                    handleAuthenticationFailure(response, "API密钥目标类型不匹配");
                    return;
                }

                // 5. 根据targetId和targetType去数据库中验证目标是否有权限
                String targetId = claims.get("targetId", String.class);
                Object apiKeyValidationResult = validateApiKeyPermission(originalApiKey, targetId, targetType);
                if (apiKeyValidationResult == null) {
                    handleAuthenticationFailure(response, "API密钥验证失败或无权限");
                    return;
                }

                // 6. 创建认证对象
                loginUser = getUserFromTokenOrApiKey(originalApiKey);
            } else if (auth.containsKey(SecurityType.TOKEN.getCode())) {
                log.debug("开始验证JWT token");

                tokenOrApiKey = auth.get(SecurityType.TOKEN.getCode());
                if (jwtUtils.validateTokenOrApiKey(tokenOrApiKey)) {
                    log.debug("JWT token验证成功");

                    // 从token中获取用户信息
                    loginUser = getUserFromTokenOrApiKey(auth.get(SecurityType.TOKEN.getCode()));
                } else {
                    log.warn("JWT token验证失败");
                }
            }

            if (loginUser != null) {
                log.debug("成功构建LoginUser: userId={}, username={}",
                        loginUser.getUserId(), loginUser.getUsername());

                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication
                        = new UsernamePasswordAuthenticationToken(
                        loginUser,
                        null,
                        loginUser.getAuthorities()
                );

                // 设置请求详情
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                // 设置到SecurityContext
                SecurityContextHolder.getContext().setAuthentication(authentication);

                log.debug("JWT认证成功，用户: {}, 租户: {}, 权限数量: {}",
                        loginUser.getUsername(), loginUser.getTenantId(),
                        loginUser.getPermissions().size());
            } else {
                log.warn("无法从JWT token构建LoginUser对象");
            }

            // 记录使用日志
            if (auth.containsKey(SecurityType.API_KEY.getCode())) {
                recordApiKeyUsage(request, tokenOrApiKey, 200, System.currentTimeMillis() - startTime, null);
            }
        } catch (Exception e) {
            log.error("JWT认证过程中发生错误: {}", e.getMessage(), e);
            // 清除SecurityContext，确保认证失败时不会有残留的认证信息
            SecurityContextHolder.clearContext();
        }

        // 继续过滤链
        filterChain.doFilter(request, response);
    }

    /**
     * 记录API密钥使用情况
     */
    private void recordApiKeyUsage(HttpServletRequest request, String compositeApiKey,
                                   int responseStatus, long responseTime, String errorMessage) {
        try {
            // 解析复合密钥获取原始密钥ID
            String originalApiKey = parseOriginalApiKey(compositeApiKey);
            if (originalApiKey != null) {
                apiKeyUsageService.recordUsage(
                        originalApiKey,
                        SecurityUtils.getClientIpAddress(request),
                        request.getRequestURI(),
                        request.getMethod(),
                        LocalDateTime.now(),
                        responseStatus,
                        responseTime,
                        errorMessage
                );
            }
        } catch (Exception e) {
            logger.warn("记录API密钥使用情况失败", e);
        }
    }

    /**
     * 从请求中提取JWT token 或 JWT ApiKey
     *
     * @param request HTTP请求
     * @return JWT token，或  JWT ApiKey ，如果不存在返回null
     */
    private Map<String, String> getAuthFromRequest(HttpServletRequest request) {
        // 从Authorization头获取token
        String authToken = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.hasText(authToken)) {
            if (authToken.startsWith(TOKEN_PREFIX)) {
                return Map.of(SecurityType.TOKEN.getCode(), authToken.substring(TOKEN_PREFIX.length()));
            } else if (authToken.startsWith(API_KEY_PREFIX)) {
                return Map.of(SecurityType.API_KEY.getCode(), authToken.substring(API_KEY_PREFIX.length()));
            }
        }

        // 从请求参数获取token（用于某些特殊场景，如WebSocket连接和SSE）
        String paramToken = request.getParameter(SecurityType.TOKEN.getCode());
        if (StringUtils.hasText(paramToken)) {
            return Map.of(SecurityType.TOKEN.getCode(), paramToken);
        }

        String apiKey = request.getParameter(SecurityType.API_KEY.getCode());
        if (StringUtils.hasText(apiKey)) {
            return Map.of(SecurityType.API_KEY.getCode(), apiKey);
        }
        return null;
    }

    /**
     * 从JWT token 或 JWT ApiKey中构建LoginUser对象
     *
     * @param token JWT token 或 JWT ApiKey
     * @return LoginUser对象
     */
    private LoginUser getUserFromTokenOrApiKey(String token) {
        try {
            Claims claims = jwtUtils.getClaimsFromTokenOrApiKey(token);
            if (claims == null) {
                log.warn("无法从JWT token获取claims");
                return null;
            }

            // 从claims中提取用户信息
            String userId = claims.getSubject();
            String username = claims.get("username", String.class);

            log.debug("从JWT token提取用户信息: userId={}, username={}",
                    userId, username);

            if (!StringUtils.hasText(userId) || !StringUtils.hasText(username)) {
                log.warn("JWT token中缺少必要的用户信息: userId={}, username={}", userId, username);
                return null;
            }

            // 首先尝试从缓存获取用户信息
            LoginUser cachedUser = userCacheService.getCachedUser(userId);
            if (cachedUser != null) {
                log.debug("从缓存获取用户信息成功: userId={}", userId);

                // 验证缓存中的用户信息是否与数据库一致（特别是tenantId）
                if (isUserInfoConsistent(cachedUser)) {
                    // 刷新缓存过期时间
                    userCacheService.refreshUserCache(userId);
                    return cachedUser;
                } else {
                    log.debug("缓存中的用户信息与数据库不一致，清除缓存并重新构建: userId={}", userId);
                    userCacheService.clearUserCache(userId);
                }
            }

            log.debug("缓存中未找到用户信息或信息不一致，从数据库构建新的LoginUser对象: userId={}", userId);

            // 从数据库获取最新的用户信息
            Object userEntity = getUserFromDatabase(userId);
            if (userEntity == null) {
                log.warn("数据库中未找到用户信息: userId={}", userId);
                return null;
            }

            // 创建LoginUser对象，使用数据库中的最新信息
            LoginUser loginUser = buildLoginUserFromEntity(userEntity);
            if (loginUser == null) {
                log.warn("构建LoginUser对象失败: userId={}", userId);
                return null;
            }

            // 从缓存或数据库获取用户的权限和角色信息
            loginUser.setPermissions(getUserPermissions(userId));
            loginUser.setRoles(getUserRoles(userId));

            // 缓存用户信息
            userCacheService.cacheUser(loginUser);

            log.debug("从数据库构建LoginUser成功: userId={}, username={}, tenantId={}",
                    loginUser.getUserId(), loginUser.getUsername(), loginUser.getTenantId());

            return loginUser;

        } catch (Exception e) {
            log.error("从JWT token构建LoginUser失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断是否跳过JWT认证 对于某些公开接口，可以跳过JWT认证
     *
     * @param request HTTP请求
     * @return 是否跳过认证
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();

        boolean shouldSkip = path.startsWith("/api/auth/")
                || path.startsWith("/api/platform/init/")
                || path.startsWith("/api/dify/test/")  // 添加Dify测试接口
                || path.startsWith("/actuator/")
                || path.startsWith("/v3/api-docs/")
                || path.startsWith("/swagger-ui/")
                || path.startsWith("/doc.html")
                || path.startsWith("/webjars/")
                || path.equals("/favicon.ico")
                || path.equals("/error")
                || path.equals("/login")
                || path.startsWith("/css/")
                || path.startsWith("/js/")
                || path.startsWith("/images/");

        log.debug("JWT过滤器shouldNotFilter检查: path={}, shouldSkip={}", path, shouldSkip);

        return shouldSkip;
    }

    /**
     * 检查是否为公开接口（不需要认证的接口）
     *
     * @param requestUri 请求URI
     * @return 是否为公开接口
     */
    private boolean isPublicEndpoint(String requestUri) {
        return requestUri.startsWith("/api/auth/")
                || requestUri.startsWith("/api/platform/init/")
                || requestUri.startsWith("/api/dify/test/")
                || requestUri.startsWith("/actuator/")
                || requestUri.startsWith("/v3/api-docs/")
                || requestUri.startsWith("/swagger-ui/")
                || requestUri.startsWith("/doc.html")
                || requestUri.startsWith("/webjars/")
                || requestUri.equals("/favicon.ico")
                || requestUri.equals("/error")
                || requestUri.equals("/login")
                || requestUri.startsWith("/css/")
                || requestUri.startsWith("/js/")
                || requestUri.startsWith("/images/");
    }

    /**
     * 获取用户权限信息 优先从缓存获取，缓存不存在时从数据库获取
     *
     * @param userId 用户ID
     * @return 用户权限集合
     */
    private Set<String> getUserPermissions(String userId) {
        try {
            // 首先尝试从缓存获取
            Set<String> cachedPermissions = userCacheService.getCachedUserPermissions(userId);
            if (!cachedPermissions.isEmpty()) {
                log.debug("从缓存获取用户权限成功: userId={}, 权限数量={}", userId, cachedPermissions.size());
                return cachedPermissions;
            }

            // 从数据库获取权限信息
            try {
                Class<?> permissionServiceClass = Class.forName("com.xhcai.modules.system.service.ISysPermissionService");
                Object permissionService = getBean(permissionServiceClass);
                @SuppressWarnings("unchecked")
                Set<String> permissions = (Set<String>) permissionServiceClass
                        .getMethod("selectPermissionCodesByUserId", String.class)
                        .invoke(permissionService, userId);

                log.debug("从数据库获取用户权限成功: userId={}, 权限数量={}", userId, permissions.size());
                return permissions != null ? permissions : Set.of();

            } catch (Exception e) {
                log.debug("从数据库获取用户权限失败: userId={}, error={}", userId, e.getMessage());
                return Set.of();
            }

        } catch (Exception e) {
            log.error("获取用户权限失败: userId={}, error={}", userId, e.getMessage(), e);
            return Set.of();
        }
    }

    /**
     * 获取用户角色信息 优先从缓存获取，缓存不存在时从数据库获取
     *
     * @param userId 用户ID
     * @return 用户角色集合
     */
    private Set<String> getUserRoles(String userId) {
        try {
            // 首先尝试从缓存获取
            Set<String> cachedRoles = userCacheService.getCachedUserRoles(userId);
            if (!cachedRoles.isEmpty()) {
                log.debug("从缓存获取用户角色成功: userId={}, 角色数量={}", userId, cachedRoles.size());
                return cachedRoles;
            }

            // 从数据库获取角色信息
            try {
                Class<?> roleServiceClass = Class.forName("com.xhcai.modules.system.service.ISysRoleService");
                Object roleService = getBean(roleServiceClass);
                @SuppressWarnings("unchecked")
                Set<String> roles = (Set<String>) roleServiceClass
                        .getMethod("selectRoleCodesByUserId", String.class)
                        .invoke(roleService, userId);

                log.debug("从数据库获取用户角色成功: userId={}, 角色数量={}", userId, roles.size());
                return roles != null ? roles : Set.of();

            } catch (Exception e) {
                log.debug("从数据库获取用户角色失败: userId={}, error={}", userId, e.getMessage());
                return Set.of();
            }

        } catch (Exception e) {
            log.error("获取用户角色失败: userId={}, error={}", userId, e.getMessage(), e);
            return Set.of();
        }
    }

    /**
     * 验证缓存中的用户信息是否与数据库一致
     *
     * @param cachedUser 缓存中的用户信息
     * @return 是否一致
     */
    private boolean isUserInfoConsistent(LoginUser cachedUser) {
        try {
            Object userEntity = getUserFromDatabase(cachedUser.getUserId());
            if (userEntity == null) {
                log.debug("数据库中未找到用户，缓存信息不一致: userId={}", cachedUser.getUserId());
                return false;
            }

            // 获取数据库中的最新tenantId
            String dbTenantId = (String) userEntity.getClass().getMethod("getTenantId").invoke(userEntity);
            String cachedTenantId = cachedUser.getTenantId();

            // 比较tenantId是否一致
            boolean tenantIdConsistent = Objects.equals(dbTenantId, cachedTenantId);
            if (!tenantIdConsistent) {
                log.debug("用户tenantId不一致 - 缓存: {}, 数据库: {}, userId={}",
                        cachedTenantId, dbTenantId, cachedUser.getUserId());
            }

            return tenantIdConsistent;

        } catch (Exception e) {
            log.debug("验证用户信息一致性失败: userId={}, error={}", cachedUser.getUserId(), e.getMessage());
            return false;
        }
    }

    /**
     * 从数据库获取用户信息
     *
     * @param userId 用户ID
     * @return 用户实体对象
     */
    private Object getUserFromDatabase(String userId) {
        try {
            // 由于用户表启用了多租户隔离，在JWT认证阶段我们还没有租户上下文
            // 直接使用Mapper的特殊方法查询，绕过租户隔离
            return getUserFromMapper(userId);

        } catch (Exception e) {
            log.debug("从数据库获取用户信息失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * 通过Mapper直接查询用户信息（绕过租户隔离）
     *
     * @param userId 用户ID
     * @return 用户实体对象
     */
    private Object getUserFromMapper(String userId) {
        try {
            // 添加一个专门的方法来查询用户信息，绕过租户隔离
            Class<?> userMapperClass = Class.forName("com.xhcai.modules.system.mapper.SysUserMapper");
            Object userMapper = getBean(userMapperClass);

            // 尝试使用自定义的查询方法
            try {
                return userMapperClass
                        .getMethod("selectByIdIgnoreTenant", String.class)
                        .invoke(userMapper, userId);
            } catch (NoSuchMethodException e) {
                // 如果没有自定义方法，使用selectById
                log.debug("未找到selectByIdIgnoreTenant方法，使用selectById: userId={}", userId);
                return userMapperClass
                        .getMethod("selectById", java.io.Serializable.class)
                        .invoke(userMapper, userId);
            }

        } catch (Exception e) {
            log.debug("通过Mapper查询用户信息失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * 从用户实体构建LoginUser对象
     *
     * @param userEntity 用户实体对象
     * @return LoginUser对象
     */
    private LoginUser buildLoginUserFromEntity(Object userEntity) {
        try {
            LoginUser loginUser = new LoginUser();

            // 使用反射获取用户实体的各个字段
            String userId = (String) userEntity.getClass().getMethod("getId").invoke(userEntity);
            String username = (String) userEntity.getClass().getMethod("getUsername").invoke(userEntity);
            String tenantId = (String) userEntity.getClass().getMethod("getTenantId").invoke(userEntity);
            String deptId = (String) userEntity.getClass().getMethod("getDeptId").invoke(userEntity);
            String nickname = (String) userEntity.getClass().getMethod("getNickname").invoke(userEntity);
            String email = (String) userEntity.getClass().getMethod("getEmail").invoke(userEntity);
            String phone = (String) userEntity.getClass().getMethod("getPhone").invoke(userEntity);
            String avatar = (String) userEntity.getClass().getMethod("getAvatar").invoke(userEntity);
            String status = (String) userEntity.getClass().getMethod("getStatus").invoke(userEntity);
            String userType = (String) userEntity.getClass().getMethod("getUserType").invoke(userEntity);

            // 设置LoginUser的各个字段
            loginUser.setUserId(userId);
            loginUser.setUsername(username);
            loginUser.setTenantId(tenantId);
            loginUser.setDeptId(deptId);
            loginUser.setNickname(nickname);
            loginUser.setEmail(email);
            loginUser.setPhone(phone);
            loginUser.setAvatar(avatar);
            loginUser.setStatus(status != null ? status : "0");
            loginUser.setUserType(userType != null ? userType : "normal");

            log.debug("从用户实体构建LoginUser成功: userId={}, username={}, tenantId={}",
                    userId, username, tenantId);

            return loginUser;

        } catch (Exception e) {
            log.error("从用户实体构建LoginUser失败: error={}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取Spring Bean
     */
    private Object getBean(Class<?> clazz) {
        try {
            Class<?> applicationContextHolderClass = Class.forName("com.xhcai.common.core.utils.ApplicationContextHolder");
            return applicationContextHolderClass.getMethod("getBean", Class.class).invoke(null, clazz);
        } catch (Exception e) {
            throw new RuntimeException("获取Bean失败: " + clazz.getName(), e);
        }
    }

    /**
     * 处理认证失败
     */
    private void handleAuthenticationFailure(HttpServletResponse response, String message)
            throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN); // 403
        response.setContentType("application/json;charset=UTF-8");

        // 构建标准的错误响应格式
        String jsonResponse = String.format(
                "{\"code\":403,\"message\":\"%s\",\"data\":null,\"success\":false,\"fail\":true,\"timestamp\":\"%s\"}",
                message,
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );

        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    private String validateCompositeApiKey(String compositeApiKey, HttpServletResponse response) throws IOException {
        try {
            // Base64解码复合密钥
            String decoded = new String(Base64.getDecoder().decode(compositeApiKey));

            // 分离原始密钥和时间戳
            int lastDotIndex = decoded.lastIndexOf('.');
            if (lastDotIndex <= 0) {
                handleAuthenticationFailure(response, "复合密钥格式错误");
                return null;
            }

            String originalApiKey = decoded.substring(0, lastDotIndex);
            String timestampStr = decoded.substring(lastDotIndex + 1);

            // 验证时间戳
            long timestamp = Long.parseLong(timestampStr);
            long currentTime = System.currentTimeMillis();
            long timeDiff = Math.abs(currentTime - timestamp);

            if (timeDiff > TIME_TOLERANCE_MINUTES * 60 * 1000) {
                handleAuthenticationFailure(response, "密钥时效性验证失败，超过30分钟时限");
                return null;
            }

            return originalApiKey;

        } catch (NumberFormatException e) {
            handleAuthenticationFailure(response, "复合密钥时间戳格式错误");
            return null;
        } catch (Exception e) {
            handleAuthenticationFailure(response, "验证复合密钥失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从复合密钥中解析出原始API密钥
     */
    private String parseOriginalApiKey(String compositeApiKey) {
        try {
            String decoded = new String(Base64.getDecoder().decode(compositeApiKey));
            int dotIndex = decoded.lastIndexOf('.');
            if (dotIndex > 0) {
                return decoded.substring(0, dotIndex);
            }
        } catch (Exception e) {
            logger.debug("解析复合密钥失败", e);
        }
        return null;
    }

    /**
     * 获取方法上的RequiresApiKey注解
     */
    private RequiresApiKey getRequiresApiKeyAnnotation(HttpServletRequest request) {
        try {
            HandlerExecutionChain handlerChain = handlerMapping.getHandler(request);
            if (handlerChain != null && handlerChain.getHandler() instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handlerChain.getHandler();

                // 先检查方法级别的注解
                RequiresApiKey methodAnnotation = handlerMethod.getMethodAnnotation(RequiresApiKey.class);
                if (methodAnnotation != null) {
                    return methodAnnotation;
                }

                // 再检查类级别的注解
                return handlerMethod.getBeanType().getAnnotation(RequiresApiKey.class);
            }
        } catch (Exception e) {
            logger.debug("获取RequiresApiKey注解失败", e);
        }
        return null;
    }

    /**
     * 验证API密钥权限
     */
    private Object validateApiKeyPermission(String originalApiKey, String targetId, String targetType) {
        try {
            // 通过反射调用SysApiKeyService的validateApiKey方法
            Class<?> apiKeyServiceClass = Class.forName("com.xhcai.modules.system.service.ISysApiKeyService");
            Object apiKeyService = getBean(apiKeyServiceClass);

            return apiKeyServiceClass
                    .getMethod("validateApiKey", String.class, String.class, String.class)
                    .invoke(apiKeyService, originalApiKey, targetId, targetType);

        } catch (Exception e) {
            log.error("验证API密钥权限失败: originalApiKey={}, targetId={}, targetType={}",
                    originalApiKey != null ? "***" : null, targetId, targetType, e);
            return null;
        }
    }
}
