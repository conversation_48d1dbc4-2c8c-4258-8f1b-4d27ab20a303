package com.xhcai.modules.rag.entity;

import java.util.Map;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 知识库实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "知识库实体")
@Entity
@Table(name = "datasets")
@TableName(value = "datasets", autoResultMap = true)
public class Dataset extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称", example = "产品知识库")
    @Column(name = "name", nullable = false, length = 255)
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "知识库描述", example = "包含产品相关的所有文档和资料")
    @Column(name = "description", columnDefinition = "TEXT")
    @TableField("description")
    private String description;

    /**
     * 文档来源类型
     */
    @Schema(description = "文档来源类型", example = "document")
    @Column(name = "data_source_type", length = 255)
    @TableField("data_source_type")
    private String dataSourceType;

    /**
     * 模型的ID
     */
    @Column(name = "model_id", length = 32)
    @TableField("model_id")
    private String modelId;

    /**
     * 检索设置 JSON格式存储检索配置，例如： {
     *   indexMode: 'high_quality',
     *   embeddingModelId: 'text-embedding-ada-002',
     *   retrievalSettings: {
     *     retrievalMode: 'hybrid',
     *     enableRerank: false,
     *     topK: 5,
     *     scoreThreshold: 0.7,
     *     rerankModelId: 'bge-reranker-large',
     *     hybridWeights: {
     *       semanticWeight: 0.7,
     *       keywordWeight: 0.3
     *     }
     *   }
     * }
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "vectorization_config", columnDefinition = "TEXT")
    @TableField(value = "vectorization_config", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> vectorizationConfig;

    /**
     * 向量数据库ID
     */
    @Schema(description = "向量数据库ID")
    @Column(name = "vector_database_id", length = 36)
    @TableField("vector_database_id")
    private String vectorDatabaseId;

    /**
     * 文件存储ID
     */
    @Schema(description = "文件存储ID")
    @Column(name = "file_storage_id", length = 36)
    @TableField("file_storage_id")
    private String fileStorageId;

    /**
     * 知识库图标
     */
    @Column(name = "icon")
    @TableField("icon")
    @Schema(description = "知识库图标", example = "📚")
    private String icon;

    /**
     * 知识库图标背景色
     */
    @Column(name = "icon_bg")
    @TableField("icon_bg")
    @Schema(description = "知识库图标背景色", example = "#3b82f6")
    private String iconBg;

    // ==================== Getters and Setters ====================
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDataSourceType() {
        return dataSourceType;
    }

    public void setDataSourceType(String dataSourceType) {
        this.dataSourceType = dataSourceType;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public Map<String, Object> getVectorizationConfig() {
        return vectorizationConfig;
    }

    public void setVectorizationConfig(Map<String, Object> vectorizationConfig) {
        this.vectorizationConfig = vectorizationConfig;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBg() {
        return iconBg;
    }

    public void setIconBg(String iconBg) {
        this.iconBg = iconBg;
    }

    public String getVectorDatabaseId() {
        return vectorDatabaseId;
    }

    public void setVectorDatabaseId(String vectorDatabaseId) {
        this.vectorDatabaseId = vectorDatabaseId;
    }

    public String getFileStorageId() {
        return fileStorageId;
    }

    public void setFileStorageId(String fileStorageId) {
        this.fileStorageId = fileStorageId;
    }

    @Override
    public String toString() {
        return "Dataset{"
                + "id='" + getId() + '\''
                + ", name='" + name + '\''
                + ", description='" + description + '\''
                + ", dataSourceType='" + dataSourceType + '\''
                + ", modelId='" + modelId + '\''
                + ", tenantId='" + getTenantId() + '\''
                + ", createBy='" + getCreateBy() + '\''
                + ", createTime=" + getCreateTime() + '\''
                + ", icon=" + getIcon() + '\''
                + ", iconBg=" + getIconBg()
                + '}';
    }
}
