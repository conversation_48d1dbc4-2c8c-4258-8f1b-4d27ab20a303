package com.xhcai.modules.rag.controller.v1;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.modules.rag.service.IOnlyOfficeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * OnlyOffice控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/v1/onlyoffice")
@Tag(name = "OnlyOffice文档预览", description = "OnlyOffice文档预览相关接口")
public class OnlyOfficeOpenApi {

    @Autowired
    private IOnlyOfficeService onlyOfficeService;

    @Operation(summary = "OnlyOffice回调", description = "处理OnlyOffice编辑器回调")
    @PostMapping("/callback")
    @RequiresApiKey(
            targetTypes = {"knowledge"},
            permissions = {"onlyoffice:callback"},
            message = "处理OnlyOffice编辑器回调"
    )
    public Map<String, Object> callback(
            @Parameter(description = "文档ID") @RequestParam(required = false) String documentId,
            @Parameter(description = "回调处理") @RequestBody Map<String, Object> callbackData) {
        
        log.info("OnlyOffice回调: documentId={}, data={}", documentId, callbackData);

        try {
            return onlyOfficeService.handleCallback(callbackData);
        } catch (Exception e) {
            log.error("处理OnlyOffice回调失败: documentId={}, error={}", documentId, e.getMessage(), e);
            return Map.of("error", 1);
        }
    }

    @Operation(summary = "文档下载", description = "文档下载")
    @RequiresApiKey(
            targetTypes = {"knowledge"},
            permissions = {"onlyoffice:download"},
            message = "文档下载"
    )
    @GetMapping("/download/{documentId}")
    public void downloadFile (
            @Parameter(description = "文档ID") @PathVariable String documentId, HttpServletResponse response) {
        try {
            onlyOfficeService.generatePreviewUrl(documentId, response);
        } catch (Exception e) {
            log.error("文档下载失败: documentId={}, error={}", documentId, e.getMessage(), e);
        }
    }
}
