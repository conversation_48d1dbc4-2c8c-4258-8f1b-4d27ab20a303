package com.xhcai.modules.rag.service.segmentation.impl;

import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import com.xhcai.modules.rag.service.segmentation.AbstractFileSegmentationProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDDocumentOutline;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDOutlineItem;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * PDF文件分段处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class PdfFileSegmentationProcessor extends AbstractFileSegmentationProcessor {
    @Override
    public List<String> getSupportedFileTypes() {
        return List.of("pdf");
    }

    @Override
    public String getProcessorName() {
        return "PDF文件分段处理器";
    }

    @Override
    public int getPriority() {
        return 20;
    }

    @Override
    public List<SegmentResult> processSegmentation(Document document, InputStream inputStream) throws Exception {
        log.info("开始处理PDF文件分段: documentId={}, fileName={}", document.getId(), document.getName());

        try {
            SegmentConfig segmentConfig = document.getSegmentConfig();
            CleaningConfig cleaningConfig = document.getCleaningConfig();
            String type = segmentConfig.getType();
            String text = "";
            if(!"directory".equals(type)){
                text = extractTextFromPdf(document, inputStream);
                text = cleanText(text, cleaningConfig);
                log.debug("PDF文档内容长度: {} 字符", text.length());
            }

            List<SegmentResult> segments = null;

            switch (type) {
                case "directory":
                    segments = processSegmentationCatalog(document, inputStream);
                    break;
                case "constantLength":
                    segments = segmentByFixedSize(text, segmentConfig.getConstantLength());
                    break;
                case "natural":
                    segments = segmentByParagraphs(text, segmentConfig.getNatural());
                    break;
                case "delimiter":
                    segments = segmentByDelimiter(text, segmentConfig.getDelimiter());
                    break;
                case "none":
                    segments = List.of(SegmentResult.create(text, 1, extractKeywords(text)));
                    break;
            }

            log.info("PDF文件分段完成: documentId={}, 分段数量={}", document.getId(), segments.size());
            return segments;
        } catch (Exception e) {
            log.error("PDF文档分段处理失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            throw new Exception("PDF文档分段处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从pdf文档中提取文本
     * @param document
     * @param inputStream
     * @return
     * @throws Exception
     */
    public String extractTextFromPdf(Document document, InputStream inputStream) throws Exception {
        try (PDDocument pdfDocument = PDDocument.load(inputStream)) {
            // 创建自定义PDF文本提取器，排除页眉页脚
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");
            // 提取PDF文本内容（不包含页眉页脚）
            return stripper.getText(pdfDocument);
        } catch (Exception e) {
            log.error("PDF文件提取文本失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
            throw new Exception("PDF文件提取文本失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按目录读取文件并进行分段
     * @param document 文档信息
     * @param inputStream 输入流
     * @return 分段结果列表
     */
    public List<SegmentResult> processSegmentationCatalog(Document document, InputStream inputStream){
        try (PDDocument pdfDocument = PDDocument.load(inputStream)) {
            // 1. 判断是否存在目录结构
            List<CatalogItem> catalogItems = extractCatalogStructure(pdfDocument, document.getCleaningConfig());

            if (catalogItems.isEmpty()) {
                log.info("PDF文档不存在目录结构，无需处理");
                return new ArrayList<>();
            }

            // 2. 按目录分段读取文件内容
            List<SegmentResult> segments = segmentByCatalog(document, catalogItems);

            log.info("按目录分段完成，共生成{}个分段", segments.size());
            return segments;
        } catch (Exception e) {
            log.error("按目录处理PDF文档分段失败: error={}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 提取PDF文档的目录结构
     * @param pdfDocument PDF文档
     * @return 目录项列表
     * @throws Exception
     */
    private List<CatalogItem> extractCatalogStructure(PDDocument pdfDocument, CleaningConfig cleaningConfig) throws Exception {
        List<CatalogItem> catalogItems = new ArrayList<>();

        log.debug("开始提取PDF目录结构，文档页数: {}", pdfDocument.getNumberOfPages());

        // 首先尝试从PDF书签（大纲）中提取目录
        try {
            PDDocumentOutline outline = pdfDocument.getDocumentCatalog().getDocumentOutline();
            if (outline != null) {
                log.debug("发现PDF书签，开始提取");
                catalogItems = extractCatalogFromOutline(outline, pdfDocument, cleaningConfig);
                if (!catalogItems.isEmpty()) {
                    log.debug("从PDF书签中提取到{}个目录项", catalogItems.size());
                    return catalogItems;
                }
            } else {
                log.debug("PDF文档没有书签");
            }
        } catch (Exception e) {
            log.warn("从PDF书签提取目录失败: {}", e.getMessage());
        }

        // 如果没有书签，尝试从文本内容中识别目录结构
        try {
            log.debug("开始从PDF文本内容识别目录结构");
            catalogItems = extractCatalogFromText(pdfDocument, cleaningConfig);
            if (!catalogItems.isEmpty()) {
                log.debug("从PDF文本中识别到{}个目录项", catalogItems.size());
            } else {
                log.debug("从PDF文本中未识别到目录结构");
            }
        } catch (Exception e) {
            log.warn("从PDF文本识别目录失败: {}", e.getMessage());
        }

        return catalogItems;
    }

    /**
     * 从PDF书签（大纲）中提取目录结构
     * @param outline PDF大纲
     * @param pdfDocument PDF文档
     * @return 目录项列表
     * @throws Exception
     */
    private List<CatalogItem> extractCatalogFromOutline(PDDocumentOutline outline, PDDocument pdfDocument, CleaningConfig cleaningConfig) throws Exception {
        List<CatalogItem> catalogItems = new ArrayList<>();

        // ✅ 优化：只提取一次全文，避免重复提取导致卡死
        String fullText = null;
        try {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");
            fullText = stripper.getText(pdfDocument);
            fullText = cleanText(fullText, cleaningConfig);
            log.debug("PDF全文提取完成，长度: {} 字符", fullText.length());
        } catch (Exception e) {
            log.warn("PDF全文提取失败: {}", e.getMessage());
            return catalogItems; // 如果无法提取文本，返回空列表
        }

        PDOutlineItem item = outline.getFirstChild();
        int index = 0;

        while (item != null) {
            extractOutlineItem(item, catalogItems, fullText, 1, index++);
            item = item.getNextSibling();
        }

        return catalogItems;
    }

    /**
     * 递归提取大纲项目
     * @param item 大纲项目
     * @param catalogItems 目录项列表
     * @param fullText 预提取的全文
     * @param level 层级
     * @param index 索引
     * @throws Exception
     */
    private void extractOutlineItem(PDOutlineItem item, List<CatalogItem> catalogItems,
                                   String fullText, int level, int index) throws Exception {

        String title = item.getTitle();
        if (title != null && !title.trim().isEmpty()) {
            // ✅ 优化：使用预提取的全文，避免重复提取
            // 这里简化处理，将全文作为内容，后续会根据标题进行分割
            String content = fullText != null ? fullText : "";

            CatalogItem catalogItem = new CatalogItem();
            catalogItem.setTitle(title.trim());
            catalogItem.setLevel(level);
            catalogItem.setStartIndex(index);
            catalogItem.setContent(new StringBuilder(content));
            catalogItems.add(catalogItem);

            log.debug("发现{}级PDF书签: {}", level, title);
        }

        // 递归处理子项目
        PDOutlineItem child = item.getFirstChild();
        int childIndex = 0;
        while (child != null) {
            extractOutlineItem(child, catalogItems, fullText, level + 1, childIndex++);
            child = child.getNextSibling();
        }
    }



    /**
     * 从PDF文本内容中识别目录结构
     * @param pdfDocument PDF文档
     * @return 目录项列表
     * @throws Exception
     */
    private List<CatalogItem> extractCatalogFromText(PDDocument pdfDocument, CleaningConfig cleaningConfig) throws Exception {
        List<CatalogItem> catalogItems = new ArrayList<>();
        StringBuilder orphanContent = new StringBuilder(); // 存储没有标题的内容

        // ✅ 优化：添加文档大小检查，避免处理过大的文档
        int pageCount = pdfDocument.getNumberOfPages();
        if (pageCount > 500) {
            log.warn("PDF文档页数过多({}页)，跳过文本目录识别以避免性能问题", pageCount);
            return catalogItems;
        }

        // 提取全文
        String fullText;
        try {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");

            // ✅ 优化：限制提取的页数，避免处理过大的文档
            if (pageCount > 200) {
                stripper.setStartPage(1);
                stripper.setEndPage(200);
                log.debug("PDF文档页数较多，仅提取前50页进行目录识别");
            }

            fullText = stripper.getText(pdfDocument);
            fullText = cleanText(fullText, cleaningConfig);

            if (fullText.length() > 100000) { // 超过10万字符
                log.warn("PDF文本内容过长({}字符)，截取前10万字符进行处理", fullText.length());
                fullText = fullText.substring(0, 100000);
            }

            log.debug("PDF文本提取完成，长度: {} 字符", fullText.length());
        } catch (Exception e) {
            log.warn("PDF文本提取失败: {}", e.getMessage());
            return catalogItems;
        }

        String[] lines = fullText.split("\n");
        log.debug("开始分析PDF文本，共{}行", lines.length);

        // ✅ 优化：限制处理的行数，避免处理过多内容
        int maxLines = Math.min(lines.length, 5000); // 最多处理5000行
        if (lines.length > maxLines) {
            log.debug("PDF文本行数过多，仅处理前{}行", maxLines);
        }

        for (int i = 0; i < maxLines; i++) {
            String line = lines[i].trim();

            if (line.isEmpty()) {
                continue;
            }

            // 检查是否为标题行
            int headingLevel = detectHeadingLevelFromText(line);
            if (headingLevel > 0) {
                // 如果有孤立内容，先创建一个默认目录项
                if (!orphanContent.isEmpty() && catalogItems.isEmpty()) {
                    CatalogItem defaultItem = new CatalogItem();
                    defaultItem.setTitle("文档内容");
                    defaultItem.setLevel(1);
                    defaultItem.setStartIndex(0);
                    defaultItem.setContent(orphanContent);
                    catalogItems.add(defaultItem);
                    log.debug("创建默认PDF目录项包含孤立内容: {} 字符", orphanContent.length());
                    orphanContent = new StringBuilder(); // 重置
                }

                CatalogItem item = new CatalogItem();
                item.setTitle(line);
                item.setLevel(headingLevel);
                item.setStartIndex(i);
                item.setContent(new StringBuilder());
                catalogItems.add(item);

                log.debug("发现{}级PDF标题: {}", headingLevel, line);
            } else {
                if (!catalogItems.isEmpty()) {
                    // 将非标题内容添加到最后一个目录项中
                    CatalogItem lastItem = catalogItems.getLast();
                    lastItem.getContent().append(line).append("\n");
                } else {
                    // 如果还没有目录项，暂存到孤立内容中
                    orphanContent.append(line).append("\n");
                }
            }
        }

        // 处理剩余的孤立内容
        if (orphanContent.length() > 0) {
            if (catalogItems.isEmpty()) {
                // 如果没有任何标题，创建一个默认目录项包含所有内容
                CatalogItem defaultItem = new CatalogItem();
                defaultItem.setTitle("PDF文档内容");
                defaultItem.setLevel(1);
                defaultItem.setStartIndex(0);
                defaultItem.setContent(orphanContent);
                catalogItems.add(defaultItem);
                log.debug("创建默认PDF目录项包含所有内容: {} 字符", orphanContent.length());
            } else {
                // 如果有标题，将剩余内容添加到最后一个目录项
                CatalogItem lastItem = catalogItems.getLast();
                lastItem.getContent().append(orphanContent);
                log.debug("将剩余PDF内容添加到最后一个目录项: {} 字符", orphanContent.length());
            }
        }

        return catalogItems;
    }
}
