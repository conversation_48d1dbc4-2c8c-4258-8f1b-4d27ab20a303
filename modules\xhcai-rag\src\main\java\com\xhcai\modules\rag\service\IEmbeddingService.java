package com.xhcai.modules.rag.service;

import com.xhcai.modules.rag.entity.DocumentSegment;
import com.xhcai.modules.rag.entity.Embedding;

import java.util.List;
import java.util.Map;

/**
 * 向量化服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IEmbeddingService {

    /**
     * 对文本进行向量化
     *
     * @param embeddingModelId 模型ID 字段的来源 models.id
     * @param text 文本内容
     * @return 向量数组
     */
    float[] embedText(String embeddingModelId, String text);

    /**
     * 批量向量化文本
     *
     * @param embeddingModelId 模型ID  字段的来源 models.id
     * @param texts 文本列表
     * @return 向量列表
     */
    List<float[]> embedTexts(String embeddingModelId, List<String> texts);

    /**
     * 计算向量相似度
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度分数
     */
    double calculateSimilarity(float[] vector1, float[] vector2);

    /**
     * 搜索相似向量
     *
     * @param queryVector 查询向量
     * @param datasetId 知识库ID
     * @param topK 返回数量
     * @return 相似分段列表
     */
    List<DocumentSegment> searchSimilarVectors(float[] queryVector, String datasetId, int topK);

    /**
     * 搜索相似文本
     *
     * @param queryText 查询文本
     * @param datasetId 知识库ID
     * @param topK 返回数量
     * @return 相似分段列表
     */
    List<DocumentSegment> searchSimilarTexts(String queryText, String datasetId, int topK);

    /**
     * 获取向量维度
     *
     * @return 向量维度
     */
    int getVectorDimension();

    /**
     * 检查向量化服务是否可用
     *
     * @param modelId 模型ID  models.id
     * @return 是否可用
     */
    boolean isAvailable(String modelId);

    /**
     * 获取向量化统计信息
     *
     * @param datasetId 知识库ID
     * @return 统计信息
     */
    Map<String, Object> getEmbeddingStats(String datasetId);

    /**
     * 删除文档的所有向量
     *
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteDocumentVectors(String documentId);

    /**
     * 删除知识库的所有向量
     *
     * @param datasetId 知识库ID
     * @return 删除数量
     */
    int deleteDatasetVectors(String datasetId);

    /**
     * 重建知识库向量索引
     *
     * @param datasetId 知识库ID
     * @return 是否成功
     */
    boolean rebuildVectorIndex(String datasetId);

    /**
     * 保存向量，将将参数 vector 转成二进制字节数组保存到数据库中
     * @param embedding 向量实体
     * @param vector    向量化的数组
     * @return 是否成功
     */
    boolean save(Embedding embedding, float[] vector);
}
