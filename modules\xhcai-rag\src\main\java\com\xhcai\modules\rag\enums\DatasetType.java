package com.xhcai.modules.rag.enums;

import lombok.Getter;

/**
 * 数据源类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum DatasetType {
    
    /**
     * 文件上传
     */
    DOCUMENT("local", "文件上传", "fa-solid fa-file"),

    /**
     * Ftp
     */
    FTP("ftp", "FTP文件", "fa-solid fa-network-wired"),

    /**
     * OBS存储
     */
    OBS("obs", "OBS存储", "fa-solid fa-network-wired"),

    /**
     * Minio存储
     */
    MINIO("minio", "Minio存储", "fa-solid fa-network-wired"),

    /**
     * API接口
     */
    API("api", "API接口", "fa-solid fa-network-wired"),
    
    /**
     * 数据库
     */
    MYSQL("mysql", "Mysql数据库", "fa-solid fa-database"),
    ORACLE("oracle", "Oracle数据库", "fa-solid fa-database"),
    POSTGRESQL("postgresql", "PostgreSQL数据库", "fa-solid fa-database"),
    
    /**
     * 文本输入
     */
    TEXT_INPUT("text_input", "文本输入", "fa-solid fa-text-height"),
    
    /**
     * 第三方集成
     */
    THIRD_PARTY("third_party", "第三方集成", "fa-solid fa-external-link"),

    /**
     * 自定义数据源
     */
    WEB_CRAWL("custom", "自定义数据源", "");

    private final String code;
    private final String description;
    private final String icon;

    DatasetType(String code, String description, String icon) {
        this.code = code;
        this.description = description;
        this.icon = icon;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 数据源类型代码
     * @return 数据源类型枚举
     */
    public static DatasetType fromCode(String code) {
        for (DatasetType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的数据源类型代码: " + code);
    }
}
