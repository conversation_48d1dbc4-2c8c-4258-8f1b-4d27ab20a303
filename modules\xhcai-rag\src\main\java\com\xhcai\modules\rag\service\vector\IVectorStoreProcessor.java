package com.xhcai.modules.rag.service.vector;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.rag.dto.KnowledgeVectorQueryDTO;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.vo.DocumentSegmentVO;
import io.weaviate.client.v1.auth.exception.AuthException;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * 向量库管理
 * <AUTHOR>
 */
public interface IVectorStoreProcessor {
    /**
     * 支持的向量数据库类型
     *
     * @return 支持的文件扩展名列表
     */
    String getSupportedVectorDbType();

    /**
     * 是否支持该向量类型
     * @return
     */
    boolean supports(String vectorDbType);

    /**
     * 创建向量库的索引, 命名规则为：Knowledge_datasetId
     * weaviate为schema下的class
     * elasticsearch为index
     * @param vectorDbId    向量库ID
     * @param datasetId     知识库ID
     */
    void createIndexIfNotExists(String vectorDbId, String datasetId) throws AuthException, Exception;

    /**
     * 存储向量
     * @param documentSegmentVO
     */
    Result<String> storeEmbeddings(DocumentSegmentVO documentSegmentVO);

    /**
     * 查询向量
     * @param knowledgeVectorQueryDTO
     * @return
     */
    List<String> listVector(KnowledgeVectorQueryDTO knowledgeVectorQueryDTO);



    /**
     * 删除向量库的class或者索引
     * @param vectorDbId    向量库ID
     * @param datasetId     知识库ID
     */
    void removeByDatasetId(String vectorDbId, String datasetId);

    /**
     * 删除向量库的class或者索引 中的某一条文档数据
     * @param vectorDbId    向量库ID
     * @param datasetId     知识库ID
     * paramm docId         id
     */
    void removeByDocId(String vectorDbId, String datasetId, String docId);
}
