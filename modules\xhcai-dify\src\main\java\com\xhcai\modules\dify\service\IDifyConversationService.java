package com.xhcai.modules.dify.service;

import com.xhcai.modules.dify.dto.conversation.DifyConversationDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyMessageListResponseDTO;

import java.util.List;

/**
 * Dify会话服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDifyConversationService {

    /**
     * 获取智能体会话列表
     *
     * @param appId 应用ID
     * @param limit 限制数量，默认100
     * @param pinned 是否只获取置顶会话，默认false
     * @return 会话列表响应
     */
    DifyConversationListResponseDTO getConversations(String appId, Integer limit, <PERSON><PERSON>an pinned);

    /**
     * 获取会话列表 - 对接 /console/api/apps/{appId}/chat-conversations 接口
     *
     * @param appId 应用ID
     * @param page 页码，默认1
     * @param limit 每页限制数量，默认10
     * @param start 开始时间，格式：2025-09-05+00%3A00
     * @param end 结束时间，格式：2025-09-12+23%3A59
     * @param sortBy 排序方式，默认-created_at
     * @param annotationStatus 标注状态，默认all
     * @return 会话列表响应
     */
    DifyConversationListResponseDTO getChatConversations(String appId, Integer page, Integer limit,
                                                         String start, String end, String sortBy, String annotationStatus);

    /**
     * 修改会话名称
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @param name 新的会话名称
     * @return 会话信息
     */
    DifyConversationDTO updateConversationName(String appId, String conversationId, String name);

    /**
     * 删除会话记录
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @return 是否成功
     */
    boolean deleteConversation(String appId, String conversationId);

    /**
     * 获取会话消息列表
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @param limit 限制数量，默认20
     * @param lastId 最后一个消息ID（用于分页）
     * @return 消息列表响应
     */
    DifyMessageListResponseDTO getConversationMessages(String appId, String conversationId, Integer limit, String lastId);
}
