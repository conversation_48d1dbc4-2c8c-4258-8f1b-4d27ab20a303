package com.xhcai.modules.rag.plugins.rabbitmq.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.enums.DocumentStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * RabbitMQ消息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RabbitMQMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消息类型
     */
    private MessageType messageType;

    /**
     * 消息内容
     */
    private Object payload;

    /**
     * 消息属性
     */
    private Map<String, Object> properties;

    /**
     * 发送者
     */
    private String sender;

    /**
     * 接收者
     */
    private String receiver;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 消息状态
     */
    private MessageStatus status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 业务标识
     */
    private String businessKey;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        /**
         * 待发送
         */
        PENDING,
        /**
         * 已发送
         */
        SENT,
        /**
         * 处理中
         */
        PROCESSING,
        /**
         * 处理成功
         */
        SUCCESS,
        /**
         * 处理失败
         */
        FAILED,
        /**
         * 已过期
         */
        EXPIRED
    }

    /**
     * 创建向量化处理消息
     */
    public static RabbitMQMessage createEmbeddingProcessingMessage(Object documentSegment, String segmentId, String tenantId, String userId) {
        return RabbitMQMessage.builder()
                .messageId(generateMessageId())
                .messageType(MessageType.EMBEDDING_PROCESSING)
                .payload(documentSegment)
                .sender("rag-service")
                .priority(3)
                .retryCount(2)
                .maxRetryCount(3)
                .createTime(LocalDateTime.now())
                .expireTime(LocalDateTime.now().plusMinutes(30))
                .status(MessageStatus.PENDING)
                .businessKey(segmentId)
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 创建文档分段处理消息
     */
    public static RabbitMQMessage createDocumentSegmentationMessage(Object segmentationData, String tenantId, String userId) {
        return RabbitMQMessage.builder()
                .messageId(generateMessageId())
                .messageType(MessageType.DOCUMENT_SEGMENTATION)
                .payload(segmentationData)
                .sender("rag-service")
                .priority(4)
                .retryCount(2)
                .maxRetryCount(3)
                .createTime(LocalDateTime.now())
                .expireTime(LocalDateTime.now().plusHours(2))
                .status(MessageStatus.PENDING)
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 创建文档状态推送消息
     */
    public static RabbitMQMessage createDocumentStatusPushMessage(Object statusData, String tenantId, String userId) {
        return RabbitMQMessage.builder()
                .messageId(generateMessageId())
                .messageType(MessageType.DOCUMENT_STATUS_PUSH)
                .payload(statusData)
                .sender("rag-service")
                .priority(2)
                .retryCount(2)
                .maxRetryCount(2)
                .createTime(LocalDateTime.now())
                .expireTime(LocalDateTime.now().plusMinutes(5))
                .status(MessageStatus.PENDING)
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 创建通知消息
     */
    public static RabbitMQMessage createNotificationMessage(Object notificationData, String tenantId, String userId) {
        return RabbitMQMessage.builder()
                .messageId(generateMessageId())
                .messageType(MessageType.NOTIFICATION)
                .payload(notificationData)
                .sender("rag-service")
                .priority(1)
                .retryCount(2)
                .maxRetryCount(2)
                .createTime(LocalDateTime.now())
                .expireTime(LocalDateTime.now().plusMinutes(10))
                .status(MessageStatus.PENDING)
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 生成消息ID
     */
    private static String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + (int) (Math.random() * 10000);
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }

    /**
     * 是否超过最大重试次数
     */
    public boolean isMaxRetryExceeded() {
        return this.retryCount != null && this.maxRetryCount != null && this.retryCount >= this.maxRetryCount;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return this.expireTime != null && LocalDateTime.now().isAfter(this.expireTime);
    }

    /**
     * 设置处理成功
     */
    public void setSuccess() {
        this.status = MessageStatus.SUCCESS;
    }

    /**
     * 设置处理失败
     */
    public void setFailed(String errorMessage) {
        this.status = MessageStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    /**
     * 设置处理中
     */
    public void setProcessing() {
        this.status = MessageStatus.PROCESSING;
    }
}
