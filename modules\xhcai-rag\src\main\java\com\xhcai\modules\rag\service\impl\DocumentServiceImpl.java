package com.xhcai.modules.rag.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.Set;
import java.util.UUID;

import com.xhcai.modules.rag.dto.BatchInfoDTO;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import com.xhcai.modules.rag.enums.FileTypeEnum;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.config.RagProperties;
import com.xhcai.modules.rag.dto.FileCleanSegmentConfigParamsDTO;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.UploadFile;
import com.xhcai.modules.rag.enums.DatasetType;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.mapper.DocumentMapper;
import com.xhcai.modules.rag.plugins.minio.MinioStorageService;
import com.xhcai.modules.rag.service.IDocumentProcessingService;
import com.xhcai.modules.rag.service.IDocumentService;
import com.xhcai.modules.rag.service.IUploadFileService;
import com.xhcai.modules.rag.vo.DocumentVO;

import cn.hutool.core.collection.CollUtil;

/**
 * 文档服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class DocumentServiceImpl extends ServiceImpl<DocumentMapper, Document> implements IDocumentService {

    private static final Logger log = LoggerFactory.getLogger(DocumentServiceImpl.class);

    @Autowired
    private RagProperties ragProperties;

    @Autowired
    private IDocumentProcessingService documentProcessingService;

    @Autowired
    private MinioStorageService minioStorageService;

    @Autowired
    private IUploadFileService uploadFileService;

    @Autowired
    private DocumentMapper documentMapper;

    private final Tika tika = new Tika();

    // 支持的文件类型
    private static final Set<String> SUPPORTED_FILE_TYPES = Set.of(
            "pdf", "doc", "docx", "txt", "md", "html", "csv", "xlsx", "pptx", "rtf"
    );

    @Override
    public IPage<DocumentVO> pageByDataset(Long current, Long size, String datasetId, String name,
            String documentStatus, Boolean enabled, String batch) {
        Page<Document> page = new Page<>(current, size);
        return baseMapper.selectPageByDataset(page, datasetId, name, documentStatus, enabled, batch);
    }

    @Override
    public List<DocumentVO> listByDatasetId(String datasetId) {
        return baseMapper.selectByDatasetId(datasetId);
    }

    @Override
    public void batchUploadDocuments(List<MultipartFile> files, String datasetId, String categoryId, String batchId, SegmentConfig segmentConfig, CleaningConfig cleaningConfig) {
        log.info("批量上传文档: fileCount={}, datasetId={}, userId={}", files.size(), datasetId);

        // 获取当前用户信息
        String currentUserId = SecurityUtils.getCurrentUserId();
        String currentTenantId = SecurityUtils.getCurrentTenantId();

//        // 验证权限
//        if (!SecurityUtils.hasPermission("rag:document:upload")) {
//            throw new BusinessException("没有文档上传权限");
//        }
        // 检查批量上传限制
        if (files.size() > ragProperties.getLimits().getMaxUploadFiles()) {
            throw new BusinessException("单次上传文件数量超过限制: " + ragProperties.getLimits().getMaxUploadFiles());
        }

        // 检查文档数量限制
        checkDocumentLimit(datasetId);
        checkTenantDocumentLimit(currentTenantId);

        files.parallelStream().forEach(file -> {

            String originalFilename = file.getOriginalFilename();
            Document document = new Document();
            document.setName(originalFilename);
            document.setBatch(batchId);
            document.setCategoryId(categoryId);

            // 生成对象名称  上传到目录格式， 知识库ID/文档ID(目录)/文档ID
            String objectName = null;
            String fileHash = null;
            try {
                // 1. 开始验证文件
                documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADING, 30, "验证文件合规性中...");

                // 2. 验证文件
                String validRet = validateFile(file);
                if (validRet != null) {
                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOAD_ERROR, 100, "验证文件：" + validRet);
                    return;
                } else {
                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADING, 40, "验证文件： 合规");
                }

                // 3. 计算文件hash值，查找当前知识库下是否已存在相同的文件
                documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADING, 50, "开始计算文件Hash值...");
                try {
                    fileHash = calculateFileHash(file);

                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADING, 60, "计算文件Hash值完成，开始从数据库查找文件是否存在");
                    // 检查是否存在相同hash的文件
                    Document existingDoc = baseMapper.selectOne(
                            new QueryWrapper<Document>()
                                    .eq("file_hash", fileHash)
                                    .eq("dataset_id", datasetId)
                    );

                    if (existingDoc != null) {
                        document.setId(existingDoc.getId());
                        documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADED, 100, "根据文件Hash找到该文件是存在，不再重复上传");
                        return;
                    }
                } catch (Exception e) {
                    log.error("计算文件hash失败: {}", e.getMessage());
                    // hash计算失败不影响文件上传，但记录错误
                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOAD_ERROR, 100, "计算文件Hash异常，请重新上传此文件");
                    return;
                }

                // 3. 构建新文件实体
                String documentId = UUID.randomUUID().toString();
                document = createDocumentFromFile(file, datasetId, currentTenantId, currentUserId, fileHash);
                document.setId(documentId);
                document.setCategoryId(categoryId);
                // 只有新上传的文件才使用默认的分段配置和清洗配置
                document.setSegmentConfig(segmentConfig);
                document.setCleaningConfig(cleaningConfig);
                objectName = datasetId + "/" + documentId + "/" + documentId + "." + document.getDocType();
                document.setBatch(batchId);

                documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADING, 70, "开始往Minio中上传文件...");

                // 4. 上传文件到文件服务器
                try (InputStream inputStream = file.getInputStream()) {
                    // 检查存储服务类型并上传文件
                    String url = minioStorageService.uploadFile(objectName, inputStream, file.getContentType());
                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADING, 80, "文件已成功上传至Minio");

                    Map<String, Object> docMetadata = document.getDocMetadata();
                    docMetadata.put("preview_url", url);
                    docMetadata.put("save_name", objectName);
                    docMetadata.put("save_type", "minio");
                    document.setDocumentStatus(DocumentStatus.UPLOADED.getCode());
                    document.setError("");
                } catch (Exception e) {
                    log.error("Failed to upload file to storage service: {}", e.getMessage(), e);
                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOAD_ERROR, 100, "上传至Minio失败，请重新上传");
                    return;
                }

                // 5. 保存文档记录
                if (saveOrUpdate(document)) {
                    // 创建上传文件记录
                    createUploadFileRecord(file, document, batchId, currentTenantId, currentUserId);
                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOADED, 100, "文件信息已成功保存至数据库");
                } else {
                    documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.SAVE_ERROR, 100, "文件信息保存失败，请重新上传");
                    log.error("批量上传文档更新Db时失败: fileName={}", file.getOriginalFilename());
                }
            } catch (Exception e) {
                log.error("批量上传文档异常: fileName={}, error={}", file.getOriginalFilename(), e.getMessage());
                documentProcessingService.pushDocumentStatusMessage(document, DocumentStatus.UPLOAD_ERROR, 100, "文件上传失败，请重新上传");
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Document updateDocument(Document document) {
        log.info("更新文档: documentId={}", document.getId());

        // 验证权限
        if (!SecurityUtils.hasPermission("rag:document:update")) {
            throw new BusinessException("没有文档更新权限");
        }

        // 检查文档是否存在
        Document existingDocument = getById(document.getId());
        if (existingDocument == null) {
            throw new BusinessException("文档不存在");
        }

        // 检查数据权限 - 只能编辑自己创建的文档或有管理权限
        String currentUserId = SecurityUtils.getCurrentUserId();
        if (!existingDocument.getCreateBy().equals(currentUserId)
                && !SecurityUtils.hasPermission("rag:document:manage")) {
            throw new BusinessException("没有权限编辑此文档");
        }

        // 检查文档是否可以编辑
        if (!canEdit(document.getId())) {
            throw new BusinessException("文档当前状态不允许编辑");
        }

        document.setUpdateTime(LocalDateTime.now());

        if (updateById(document)) {
            log.info("文档更新成功: documentId={}", document.getId());
            return getById(document.getId());
        } else {
            throw new BusinessException("文档更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDocument(String id) {
        log.info("删除文档: documentId={}", id);

        // 验证权限
//        if (!SecurityUtils.hasPermission("rag:document:delete")) {
//            throw new BusinessException("没有文档删除权限");
//        }

        // 检查文档是否存在
        Document document = getById(id);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        // 检查数据权限 - 只能删除自己创建的文档或有管理权限
        String currentUserId = SecurityUtils.getCurrentUserId();
//        if (!document.getCreateBy().equals(currentUserId)
//                && !SecurityUtils.hasPermission("rag:document:manage")) {
//            throw new BusinessException("没有权限删除此文档");
//        }

        // 检查文档是否可以删除
        if (!canDelete(id)) {
            throw new BusinessException("文档当前状态不允许删除");
        }

        // TODO: 删除相关的文档分段和向量数据
        // deleteDocumentSegments(id);
        // deleteDocumentVectors(id);
        if (removeById(id)) {
            log.info("文档删除成功: documentId={}", id);
            return true;
        } else {
            throw new BusinessException("文档删除失败");
        }
    }

    @Override
    public Document getDocumentById(String id) {
        return getById(id);
    }

    @Override
    public Long countByDatasetId(String datasetId) {
        return baseMapper.countByDatasetId(datasetId);
    }

    @Override
    public Long countByDatasetIdAndStatus(String datasetId, String documentStatus) {
        return baseMapper.countByDatasetIdAndStatus(datasetId, documentStatus);
    }

    /**
     * 验证上传的文件
     */
    private String validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "文件不能为空";
        }

        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            return "文件名不能为空";
        }

        // 检查文件大小
        if (file.getSize() > ragProperties.getLimits().getMaxDocumentSize()) {
            return "文件大小超过限制: " + ragProperties.getLimits().getMaxDocumentSize() + " 字节";
        }

        // 检查文件类型
        String fileExtension = getFileExtension(originalFilename);
        if (FileTypeEnum.fromCode(fileExtension.toLowerCase()) == null) {
            return "不支持的文件类型: " + fileExtension;
        }

        // 使用Tika检测文件内容类型，验证文件真实性
//        try (InputStream inputStream = file.getInputStream()) {
//            String detectedType = tika.detect(inputStream, originalFilename);
//            // 验证文件内容与扩展名是否匹配
//            if (!isValidFileType(detectedType, fileExtension)) {
//                return "文件内容与扩展名不匹配，可能是恶意文件";
//            }
//        } catch (IOException e) {
//            log.error("文件内容验证失败: {}", e.getMessage());
//            return "文件内容验证失败: " + e.getMessage();
//        }
        return null;
    }

    /**
     * 验证文件内容类型与扩展名是否匹配
     */
//    private boolean isValidFileType(String detectedType, String fileExtension) {
//        if (detectedType == null) {
//            return false;
//        }
//
//        String lowerExtension = fileExtension.toLowerCase();
//        String lowerDetectedType = detectedType.toLowerCase();
//
//        // 定义文件类型映射
//        Map<String, List<String>> typeMapping = Map.of(
//                "pdf", List.of("application/pdf"),
//                "doc", List.of("application/msword"),
//                "docx", List.of("application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
//                "txt", List.of("text/plain", "text/x-c", "text/x-java"),
//                "md", List.of("text/plain", "text/x-web-markdown"),
//                "rtf", List.of("application/rtf", "text/rtf")
//        );
//
//        List<String> validTypes = typeMapping.get(lowerExtension);
//        if (validTypes == null) {
//            return false;
//        }
//
//        return validTypes.stream().anyMatch(lowerDetectedType::contains);
//    }

    /**
     * 从上传文件创建文档记录
     */
    private Document createDocumentFromFile(MultipartFile file, String datasetId, String tenantId, String userId, String fileHash) {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);

        Document document = new Document();
        document.setDatasetId(datasetId);
        document.setTenantId(tenantId);
        document.setName(originalFilename);
        document.setDataSourceType(DatasetType.DOCUMENT.getCode());
        document.setBatch(generateBatchId());
        document.setDocumentStatus(DocumentStatus.UPLOADING.getCode());
        document.setEnabled(true);
        document.setArchived(false);
        document.setIsPaused(false);
        document.setUploadTime(LocalDateTime.now());
        document.setUploadBy(userId);
        document.setFileSize(file.getSize());
        document.setDocType(fileExtension);
        document.setDocForm("text_model");
        document.setDocLanguage("zh");
        document.setUpdateTime(LocalDateTime.now());
        document.setUpdateBy(userId);
        document.setCreateBy(userId);
        document.setFileHash(fileHash);

        // 计算文件字符数
        try {
            int wordCount = calculateWordCount(file);
            document.setWordCount(wordCount);
        } catch (Exception e) {
            log.error("计算文件字符数失败: {}", e.getMessage());
            document.setWordCount(0);
        }

        // 设置文档元数据
        Map<String, Object> docMetadata = new HashMap<>();

        docMetadata.put("content_type", file.getContentType());
        document.setDocMetadata(docMetadata);

        return document;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 构建数据源信息
     */
    private String buildDataSourceInfo(MultipartFile file) {
        Map<String, Object> info = new HashMap<>();
        info.put("filename", file.getOriginalFilename());
        info.put("size", file.getSize());
        info.put("content_type", file.getContentType());
        info.put("upload_time", LocalDateTime.now().toString());

        // TODO: 这里应该保存文件到存储系统并返回文件路径
        // String filePath = fileStorageService.saveFile(file);
        // info.put("file_path", filePath);
        return info.toString();
    }

    /**
     * 生成批次ID
     */
    private String generateBatchId() {
        return "batch_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "_" + UUID.randomUUID();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startProcessing(String documentId) {
        log.info("开始处理文档: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        if (!DocumentStatus.INDEX_WAITING.getCode().equals(document.getDocumentStatus())
                && !DocumentStatus.INDEX_ERROR.getCode().equals(document.getDocumentStatus())) {
            throw new BusinessException("文档当前状态不允许开始处理");
        }

        document.setDocumentStatus(DocumentStatus.INDEXING.getCode());
        document.setProcessingStartedAt(LocalDateTime.now());
        document.setError(null);
        document.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(document);
        if (result) {
            log.info("文档开始处理成功: documentId={}", documentId);
            // TODO: 触发异步处理任务
            // processDocumentAsync(documentId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pauseProcessing(String documentId, String userId) {
        log.info("暂停文档处理: documentId={}, userId={}", documentId, userId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        if (!DocumentStatus.INDEXING.getCode().equals(document.getDocumentStatus())) {
            throw new BusinessException("只有处理中的文档才能暂停");
        }

        int result = baseMapper.pauseDocument(documentId, userId);
        if (result > 0) {
            log.info("文档暂停处理成功: documentId={}", documentId);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEnabled(String documentId, Boolean enabled, String userId) {
        log.info("更新文档启用状态: documentId={}, enabled={}, userId={}", documentId, enabled, userId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        int result = baseMapper.updateEnabled(documentId, enabled, userId);
        if (result > 0) {
            log.info("文档启用状态更新成功: documentId={}, enabled={}", documentId, enabled);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean archiveDocument(String documentId, String reason, String userId) {
        log.info("归档文档: documentId={}, reason={}, userId={}", documentId, reason, userId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        if (document.getArchived()) {
            throw new BusinessException("文档已归档");
        }

        int result = baseMapper.archiveDocument(documentId, reason, userId);
        if (result > 0) {
            log.info("文档归档成功: documentId={}", documentId);
        }

        return result > 0;
    }

    @Override
    public Object getDocumentStats(String documentId) {
        log.info("获取文档统计信息: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new BusinessException("文档不存在");
        }

        Map<String, Object> stats = new HashMap<>();
        stats.put("documentId", documentId);
        stats.put("name", document.getName());
        stats.put("datasetId", document.getDatasetId());
        stats.put("dataSourceType", document.getDataSourceType());
        stats.put("docType", document.getDocType());
        stats.put("docForm", document.getDocForm());
        stats.put("docLanguage", document.getDocLanguage());

        // 基本统计
        stats.put("wordCount", document.getWordCount());
        stats.put("tokens", document.getTokens());
        stats.put("indexingLatency", document.getIndexingLatency());

        // 状态信息
        stats.put("documentStatus", document.getDocumentStatus());
        stats.put("enabled", document.getEnabled());
        stats.put("archived", document.getArchived());

        // 时间信息
        stats.put("createTime", document.getCreateTime());
        stats.put("updatedAt", document.getUpdateTime());
        stats.put("processingStartedAt", document.getProcessingStartedAt());
        stats.put("completedAt", document.getCompletedAt());

        // 元数据
        stats.put("metadata", document.getDocMetadata());
        return stats;
    }

    @Override
    public List<Document> getWaitingDocuments(Integer limit) {
        return baseMapper.selectWaitingDocuments(limit);
    }

    @Override
    public List<Document> getProcessingDocuments() {
        return baseMapper.selectProcessingDocuments();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProcessingStatus(String documentId, String status) {
        log.info("更新文档处理状态: documentId={}, status={}", documentId, status);

        int result = baseMapper.updateDocumentStatus(documentId, status);
        if (result > 0) {
            log.info("文档处理状态更新成功: documentId={}, status={}", documentId, status);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProcessingProgress(String documentId, String status, Integer wordCount, Integer tokens) {
        log.info("更新文档处理进度: documentId={}, status={}, wordCount={}, tokens={}",
                documentId, status, wordCount, tokens);

        int result = baseMapper.updateProcessingProgress(documentId, status, wordCount, tokens);
        if (result > 0) {
            log.info("文档处理进度更新成功: documentId={}", documentId);
        }

        return result > 0;
    }

    @Override
    public List<DocumentVO> listByBatch(String batch) {
        return baseMapper.selectByBatch(batch);
    }

    @Override
    public boolean canDelete(String documentId) {
        Document document = getById(documentId);
        if (document == null) {
            return false;
        }

        // 处理中的文档不能删除
        if (DocumentStatus.PROCESSING.getCode().equals(document.getDocumentStatus())) {
            return false;
        }

        // 已归档的文档不能删除
        if (document.getArchived()) {
            return false;
        }

        return true;
    }

    @Override
    public boolean canEdit(String documentId) {
        Document document = getById(documentId);
        if (document == null) {
            return false;
        }

        // 处理中的文档不能编辑
        if (DocumentStatus.PROCESSING.getCode().equals(document.getDocumentStatus())) {
            return false;
        }

        // 已归档的文档不能编辑
        if (document.getArchived()) {
            return false;
        }

        return true;
    }

    /**
     * 检查文档数量限制
     */
    private void checkDocumentLimit(String datasetId) {
        Long documentCount = countByDatasetId(datasetId);
        if (documentCount >= ragProperties.getLimits().getMaxDocumentsPerDataset()) {
            throw new BusinessException("知识库文档数量已达上限: " + ragProperties.getLimits().getMaxDocumentsPerDataset());
        }
    }

    /**
     * 检查租户文档限制
     */
    private void checkTenantDocumentLimit(String tenantId) {
        // TODO: 实现租户级别的文档数量限制检查
        // Long tenantDocumentCount = countByTenantId(tenantId);
        // if (tenantDocumentCount >= ragProperties.getLimits().getMaxDocumentsPerTenant()) {
        //     throw new BusinessException("租户文档数量已达上限");
        // }
    }

    /**
     * 计算文件的SHA-256 hash值
     */
    private String calculateFileHash(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");

        try (InputStream inputStream = file.getInputStream()) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
        }

        byte[] hashBytes = digest.digest();
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }

    /**
     * 计算文件的字符数
     */
    private int calculateWordCount(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            String content = parseDocumentContent(inputStream, file.getOriginalFilename());
            if (content == null || content.trim().isEmpty()) {
                return 0;
            }

            // 简单的字符数计算，去除空白字符
            return content.replaceAll("\\s+", "").length();
        } catch (Exception e) {
            log.error("解析文档内容失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 解析文档内容
     */
    private String parseDocumentContent(InputStream inputStream, String filename) throws IOException, TikaException {
        try {
            return tika.parseToString(inputStream);
        } catch (Exception e) {
            log.error("使用Tika解析文档失败: {}", e.getMessage());
            // 如果Tika解析失败，对于文本文件尝试直接读取
            if (filename != null && filename.toLowerCase().endsWith(".txt")) {
                try (Scanner scanner = new Scanner(inputStream, "UTF-8")) {
                    scanner.useDelimiter("\\A");
                    return scanner.hasNext() ? scanner.next() : "";
                }
            }
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteDocuments(List<String> documentIds) {
        if (CollUtil.isEmpty(documentIds)) {
            return;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        log.info("批量删除文档开始 - tenantId={}, userId={}, documentIds={}", tenantId, currentUserId, documentIds);

        for (String documentId : documentIds) {
            try {
                // 检查文档是否存在且属于当前租户
                Document document = getById(documentId);
                if (document == null || !tenantId.equals(document.getTenantId())) {
                    log.warn("文档不存在或无权限删除 - documentId={}", documentId);
                    continue;
                }

                // 检查是否可以删除
                if (!canDelete(documentId)) {
                    log.warn("文档不能删除 - documentId={}", documentId);
                    continue;
                }

                // 删除文档
                deleteDocument(documentId);
                log.info("文档删除成功 - documentId={}", documentId);

            } catch (Exception e) {
                log.error("删除文档失败 - documentId={}, error={}", documentId, e.getMessage(), e);
                // 继续处理其他文档，不中断整个批量操作
            }
        }

        log.info("批量删除文档完成 - documentIds={}", documentIds);
    }

    @Override
    public List<DocumentVO> listByCategoryId(String categoryId) {
        return baseMapper.selectByCategoryId(categoryId);
    }

    @Override
    public Long countByCategoryId(String categoryId) {
        return baseMapper.countByCategoryId(categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDocumentCategory(String documentId, String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 检查文档是否存在且属于当前租户
        Document document = getById(documentId);
        if (document == null || !tenantId.equals(document.getTenantId())) {
            throw new BusinessException("文档不存在或无权限操作");
        }

        baseMapper.updateCategory(documentId, categoryId);
        log.info("更新文档分类成功 - documentId={}, categoryId={}", documentId, categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateDocumentCategory(List<String> documentIds, String categoryId) {
        if (CollUtil.isEmpty(documentIds)) {
            return;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        log.info("批量更新文档分类开始 - tenantId={}, userId={}, documentIds={}, categoryId={}",
                tenantId, currentUserId, documentIds, categoryId);

        // 检查所有文档是否存在且属于当前租户
        for (String documentId : documentIds) {
            Document document = getById(documentId);
            if (document == null || !tenantId.equals(document.getTenantId())) {
                throw new BusinessException("文档不存在或无权限操作: " + documentId);
            }
        }

        // 批量更新分类
        int updatedCount = baseMapper.batchUpdateCategory(documentIds, categoryId);
        log.info("批量更新文档分类完成 - documentIds={}, categoryId={}, updatedCount={}",
                documentIds, categoryId, updatedCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveDocumentConfigs(FileCleanSegmentConfigParamsDTO request) {
        try {
            // 获取当前用户信息
            String currentUserId = SecurityUtils.getCurrentUserId();
            String tenantId = SecurityUtils.getCurrentTenantId();

            // 验证权限
//            if (!SecurityUtils.hasPermission("rag:document:update")) {
//                throw new BusinessException("没有文档更新权限");
//            }

            // 1. 保存全局配置（如果需要的话，可以保存到系统配置表）
            // 这里暂时跳过全局配置的持久化，因为全局配置通常在前端管理

            // 2. 批量更新有自定义配置的文档
            if (request.getDocumentIds() != null && !request.getDocumentIds().isEmpty()) {
                List<Document> documentsToUpdate = new ArrayList<>();
                for (int i = 0; i < request.getDocumentIds().size(); i++) {
                    String documentId = request.getDocumentIds().get(i);
                       // 检查权限
//                    if (!document.getCreateBy().equals(currentUserId)
//                        && !SecurityUtils.hasPermission("rag:document:manage")) {
//                        log.warn("没有权限更新文档配置: documentId={}", documentId);
//                        continue;
//                    }

                    Document document = getById(documentId);
                    if (document == null) {
                        log.warn("文档不存在，跳过配置保存: documentId={}", documentId);
                        continue;
                    }

                    if (document != null) {
                        // 对指定文件特殊配置进行保存
                        if (request.getDocConfigs() !=null && request.getDocConfigs().containsKey(documentId)) {
                            document.setSegmentConfig(request.getDocConfigs().get(documentId).getSegmentConfig());
                            document.setCleaningConfig(request.getDocConfigs().get(documentId).getCleaningConfig());
                            document.setUpdateTime(LocalDateTime.now());
                            documentsToUpdate.add(document);
                        } else {
                            // 将默认SegmentConfig和CleaningConfig配置信息保存到文档对象中
                            document.setSegmentConfig(request.getSegmentConfig());
                            document.setCleaningConfig(request.getCleaningConfig());
                            document.setUpdateTime(LocalDateTime.now());
                            documentsToUpdate.add(document);

                        }
                    }
                }
                // 批量更新文档
                if (!documentsToUpdate.isEmpty()) {
                    boolean updateResult = updateBatchById(documentsToUpdate);
                    if (updateResult) {
                        log.info("批量保存文档配置成功: 更新文档数量={}", documentsToUpdate.size());
                    } else {
                        log.error("批量保存文档配置失败");
                        return false;
                    }
                }
            }

            return true;

        } catch (Exception e) {
            log.error("批量保存文档配置失败: {}", e.getMessage(), e);
            throw new BusinessException("批量保存文档配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建上传文件记录
     *
     * @param file 上传的文件
     * @param document 文档记录
     * @param batchId 批次ID
     * @param tenantId 租户ID
     * @param userId 用户ID
     */
    private void createUploadFileRecord(MultipartFile file, Document document, String batchId, String tenantId, String userId) {
        try {
            UploadFile uploadFile = new UploadFile();
            uploadFile.setOriginalFilename(file.getOriginalFilename());
            uploadFile.setFileSize(file.getSize());
            uploadFile.setFileExtension(getFileExtension(file.getOriginalFilename()));
            uploadFile.setMimeType(file.getContentType());
            uploadFile.setFileHash(document.getFileHash());
            uploadFile.setDatasetId(document.getDatasetId());
            uploadFile.setBatchId(batchId);
            uploadFile.setDocumentId(document.getId());
            uploadFile.setUploadUserId(userId);
            uploadFile.setOperationType("upload");
            uploadFile.setCreateBy(userId);
            uploadFile.setUpdateBy(userId);
            uploadFile.setTenantId(tenantId);
            uploadFile.setUploadTime(LocalDateTime.now());
            uploadFile.setOperationTime(LocalDateTime.now());

            // 从文档元数据中获取存储信息
            Map<String, Object> docMetadata = document.getDocMetadata();
            if (docMetadata != null) {
                uploadFile.setMinioUrl((String) docMetadata.get("preview_url"));
                uploadFile.setMinioObjectName((String) docMetadata.get("save_name"));
                // 假设使用默认的存储桶名称
                uploadFile.setMinioBucket("xhcai-plus");
            }

            // 根据文档状态设置上传状态
            if (DocumentStatus.UPLOADED.getCode().equals(document.getDocumentStatus())) {
                uploadFile.setUploadStatus("uploaded");
            } else if (DocumentStatus.UPLOAD_ERROR.getCode().equals(document.getDocumentStatus())) {
                uploadFile.setUploadStatus("failed");
                uploadFile.setErrorMessage(document.getError());
            } else {
                uploadFile.setUploadStatus("uploading");
            }

            // 保存上传文件记录
            uploadFileService.save(uploadFile);
            log.info("创建上传文件记录成功: fileName={}, documentId={}", file.getOriginalFilename(), document.getId());

        } catch (Exception e) {
            log.error("创建上传文件记录失败: fileName={}, documentId={}, error={}",
                     file.getOriginalFilename(), document.getId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    public List<BatchInfoDTO> getBatchList(String datasetId, String categoryId) {
        log.info("获取知识库批次列表: datasetId={}", datasetId);
        try {
            List<BatchInfoDTO> batchList = documentMapper.selectBatchList(datasetId, categoryId);
            log.info("获取知识库批次列表成功: datasetId={}, batchCount={}", datasetId, batchList.size());
            return batchList;
        } catch (Exception e) {
            log.error("获取知识库批次列表失败: datasetId={}, error={}", datasetId, e.getMessage(), e);
            throw new BusinessException("获取批次列表失败: " + e.getMessage());
        }
    }
}
